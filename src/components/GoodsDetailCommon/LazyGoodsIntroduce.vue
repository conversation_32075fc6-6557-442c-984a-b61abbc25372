<!--
/**
 * 懒加载商品介绍组件
 *
 * 主要功能：
 * 1. 智能懒加载商品介绍内容，优化页面性能和用户体验
 * 2. 基于Intersection Observer API实现精确的可视区域检测
 * 3. 用户滚动行为检测，避免初始页面过度加载
 * 4. 骨架屏占位符，提供流畅的加载体验
 * 5. 特殊处理京东商品的动态数据加载
 *
 * 技术特点：
 * - 复杂的懒加载策略，结合用户行为和可视区域判断
 * - Intersection Observer性能优化，避免频繁的滚动事件监听
 * - 智能的初始视口检测，防止不必要的预加载
 * - 异步数据加载机制，支持动态内容获取
 * - 完善的资源清理，避免内存泄漏
 *
 * 使用场景：
 * - 商品详情页的商品介绍懒加载
 * - 需要优化首屏加载性能的长页面
 * - 包含大量富文本内容的页面组件
 */
-->

<template>
  <!-- 商品介绍懒加载容器 -->
  <section class="introduce-section" ref="introduceSectionRef">
    <!-- 懒加载占位符 -->
    <!-- 在内容未加载时显示骨架屏，提供良好的用户体验 -->
    <div v-if="!isIntroduceVisible" class="introduce-placeholder">
      <div class="introduce-placeholder__title">商品信息</div>
      <div class="introduce-placeholder__content">
        <div class="introduce-placeholder__skeleton"></div>
      </div>
    </div>
    
    <!-- 实际商品介绍组件 -->
    <!-- 当内容可见时渲染真实的商品介绍组件 -->
    <GoodsIntroduce v-else :currentSKU="currentSKUForIntroduce" />
  </section>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, toRefs } from 'vue'
import GoodsIntroduce from './GoodsIntroduce.vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 当前选中的SKU对象，包含商品基础信息
  currentSku: {
    type: Object,
    default: () => ({})
  },
  // 商品介绍数据，可能是异步加载的富文本内容
  productIntroductionData: {
    type: String,
    default: ''
  }
})

// 使用toRefs解构props，保持响应性
const { currentSku, productIntroductionData } = toRefs(props)

// 定义组件事件
const emit = defineEmits(['load-introduce-data'])

// ===================== DOM引用和状态管理 ======================
// 商品介绍区域的DOM引用，用于Intersection Observer监听
const introduceSectionRef = ref(null)
// 商品介绍是否可见的状态标识
const isIntroduceVisible = ref(false)
// 商品介绍数据是否已加载完成的状态标识
const hasIntroduceDataLoaded = ref(false)
// 用户是否已经滚动过页面的状态标识
const hasUserScrolled = ref(false)

// ===================== 计算属性 ======================
// 懒加载的商品介绍数据，根据加载状态动态组合SKU信息
const currentSKUForIntroduce = computed(() => {
  // 如果商品介绍不可见，返回空对象避免不必要的渲染
  if (!isIntroduceVisible.value) {
    return {}
  }

  // 返回基础SKU数据，根据数据加载状态决定是否包含介绍内容
  return {
    ...currentSku.value,
    introduction: hasIntroduceDataLoaded.value ? productIntroductionData.value : ''
  }
})

// ===================== 全局变量 ======================
// Intersection Observer实例，用于监听商品介绍区域的可见性
let introduceObserver = null

// ===================== 事件处理函数 ======================
// 监听用户滚动行为，用于智能懒加载策略
const handleUserScroll = () => {
  if (!hasUserScrolled.value) {
    hasUserScrolled.value = true
    console.log('检测到用户滚动行为')

    // 如果商品介绍还没有加载，重新检查是否应该加载
    if (!isIntroduceVisible.value && introduceSectionRef.value) {
      const rect = introduceSectionRef.value.getBoundingClientRect()
      const viewportHeight = window.innerHeight

      // 如果区域已经进入可视区域（包含50px的预加载边距），立即加载
      if (rect.top < viewportHeight + 50 && rect.bottom > -50) {
        console.log('用户滚动后检测到商品介绍区域在可视区域内，立即加载')
        isIntroduceVisible.value = true
        loadIntroduceData()

        // 停止观察，避免重复触发
        if (introduceObserver) {
          introduceObserver.unobserve(introduceSectionRef.value)
        }
      }
    }
  }
}

// ===================== 核心业务逻辑 ======================
// 初始化懒加载观察器，实现智能的可视区域检测
const initIntroduceObserver = () => {
  if (!introduceSectionRef.value) return

  // 创建Intersection Observer实例
  introduceObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        console.log('观察器触发:', {
          isIntersecting: entry.isIntersecting,
          isIntroduceVisible: isIntroduceVisible.value,
          hasUserScrolled: hasUserScrolled.value,
          isInInitialViewport: isInInitialViewport(entry.target)
        })

        // 当元素进入可视区域且尚未加载时
        if (entry.isIntersecting && !isIntroduceVisible.value) {
          // 智能加载策略：只有在用户滚动过或者区域不在初始可视区域时才加载
          if (hasUserScrolled.value || !isInInitialViewport(entry.target)) {
            console.log('商品介绍区域进入可视区域，开始加载')
            isIntroduceVisible.value = true

            // 开始加载数据
            loadIntroduceData()

            // 停止观察，避免重复触发
            introduceObserver.unobserve(entry.target)
          } else {
            console.log('区域在初始视口内且用户未滚动，暂不加载')
          }
        }
      })
    },
    {
      // 提前50px开始加载，平衡性能和用户体验
      rootMargin: '50px 0px',
      // 当10%的元素可见时触发
      threshold: 0.1
    }
  )

  // 开始观察目标元素
  introduceObserver.observe(introduceSectionRef.value)
}

// 检查元素是否在初始视口内，用于智能懒加载策略
const isInInitialViewport = (element) => {
  const rect = element.getBoundingClientRect()
  const viewportHeight = window.innerHeight || document.documentElement.clientHeight

  // 判断元素是否在初始视口内：元素顶部在视口内且底部也在视口范围内
  return rect.top < viewportHeight && rect.bottom > 0
}

// 加载商品介绍数据，支持异步数据获取
const loadIntroduceData = async () => {
  if (!currentSku.value) {
    hasIntroduceDataLoaded.value = true
    return
  }

  try {
    // 特殊处理京东商品，需要动态请求接口获取介绍数据
    if (currentSku.value.supplierCode && currentSku.value.supplierCode.indexOf('jd_') > -1) {
      console.log('开始请求京东商品介绍数据')
      emit('load-introduce-data')
    }

    // 标记数据加载完成
    hasIntroduceDataLoaded.value = true
    console.log('商品介绍数据加载完成')
  } catch (error) {
    console.error('加载商品介绍数据失败:', error)
    // 即使加载失败也要标记为完成，避免无限重试
    hasIntroduceDataLoaded.value = true
  }
}

// 初始化方法，设置懒加载机制
const init = () => {
  // 延迟初始化，确保页面稳定后再开始监听
  // 500ms的延迟可以避免页面初始化时的性能问题
  setTimeout(() => {
    initIntroduceObserver()
    // 开始监听用户滚动，使用passive选项优化性能
    window.addEventListener('scroll', handleUserScroll, { passive: true })
  }, 500)
}

// ===================== 生命周期和组件接口 ======================
// 组件挂载时初始化懒加载机制
onMounted(() => {
  init()
})

// 组件卸载时清理资源，避免内存泄漏
onUnmounted(() => {
  // 清理滚动事件监听器
  window.removeEventListener('scroll', handleUserScroll)

  // 清理Intersection Observer
  if (introduceObserver) {
    introduceObserver.disconnect()
    introduceObserver = null
  }
})

// 暴露初始化方法给父组件，支持手动重新初始化
defineExpose({
  init
})
</script>

<style scoped lang="less">
// 商品介绍区域 - 标准padding
.introduce-section {
  background-color: #FFFFFF;
  padding: 10px 0;
  box-sizing: border-box;
}

.introduce-placeholder {
  padding: 20px;
  background-color: #FFFFFF;

  &__title {
    font-size: 16px;
    font-weight: 500;
    color: #171E24;
    margin-bottom: 16px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__skeleton {
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}
</style>
