<!--
/**
 * 企业管理端商品详情页面组件
 *
 * @description 企业管理端的商品详情展示页面，提供完整的商品信息查看、规格选择、购买等功能
 *
 * @features
 * - 商品详情展示：商品图片轮播、基础信息、规格选择、配送信息
 * - 规格管理：支持多规格商品的规格选择和切换
 * - 购物车功能：添加到购物车、立即购买
 * - 地址管理：配送地址选择和验证
 * - 营销活动：支持各种营销活动和优惠券
 * - 权限控制：白名单用户限制、区域销售限制
 * - 状态管理：商品上下架状态、库存状态、用户购买资格
 *
 * @technical
 * - 使用 Vue 3 Composition API
 * - 集成 Pinia 状态管理
 * - 支持京东商品特殊处理
 * - 防抖优化性能
 * - 响应式数据绑定
 * - 组件化架构
 *
 * @usage
 * - 企业管理员查看商品详情
 * - 企业用户进行商品购买
 * - 支持从商品列表页面跳转进入
 *
 * <AUTHOR>
 * @since 1.0.0
 */
-->

<template>
  <div class="goods-detail">
    <!-- 页面加载骨架屏：在数据加载时显示，提升用户体验 -->
    <GoodsDetailSkeleton v-if="isLoading" />

    <!-- 商品详情主要内容区域：数据加载完成后显示 -->
    <div v-else class="goods-content">
      <!-- 商品图片轮播区域：展示商品的多张图片，支持点击预览和自动轮播 -->
      <section class="image-section">
        <GoodsSwiper
          :imageList="goodsMediaList"
          :loop="true"
          :autoplay="true"
          mode="square"
          @image-click="handleImagePreview"
          @slide-change="handleSlideChange"
        />
      </section>

      <!-- 商品基础信息区域：显示商品名称、价格、原价等基本信息 -->
      <GoodsBasicInfo :goods-info="goodsInfo" />

      <!-- 商品规格选择区域：支持多规格商品的规格选择和数量调整 -->
      <SpecSelection
        ref="specSelectionRef"
        :selected-spec="selectedSpec"
        :spec-options="enhancedSpecOptions"
        @spec-click="showSpecPopup = true"
        @select-spec="selectSpec"
      />

      <!-- 配送信息区域：显示配送地址、预计送达时间、退货政策等信息 -->
      <DeliveryInfo
        :delivery-info="deliveryInfo"
        :logistics-info="logisticsServicesInfo"
        :is-j-d="isJD"
        :show-logistics-services="isShowLogisticsServices"
        @address-click="handleAddressClick"
      />
    </div>

    <!-- 底部操作栏占位符：为固定在底部的操作栏预留空间 -->
    <WoActionBarPlaceholder />

    <!-- 商品状态提示信息：显示商品的各种状态提示，如缺货、下架等 -->
    <StatusTips
      :is-data-get="isDataGet"
      :on-sale-state="onSaleState"
      :stock-state="stockState"
      :user-status="userStatus"
      :regional-sales-state="regionalSalesState"
      :limit-state="limitState"
    />

    <!-- 底部操作栏：固定在页面底部，包含购物车、加入购物车、立即购买等操作 -->
    <ActionBar
      :cart-count="cartCount"
      :cart-button-disabled="cartButtonDisabledStatus"
      @go-to-cart="goToCart"
      @add-to-cart="addToCart"
      @buy-now="buyNow"
    />

    <!-- 规格选择弹窗：点击规格选择时弹出，支持规格选择和数量调整 -->
    <SpecSelectionPopup
      v-model:visible="showSpecPopup"
      :address-info="addressInfo"
      :goods-info="skuGoodsInfo"
      :spec-options="specOptions"
      :initial-quantity="goodsNum"
      :action-type="specActionType"
      :cart-button-disabled="cartButtonDisabledStatus"
      @select-spec="selectSpec"
      @add-to-cart="handleAddToCart"
      @buy-now="handleBuyNow"
      @quantity-change="handleQuantityChange"
    />

    <!-- 地址选择弹窗：点击配送地址时弹出，支持快速选择配送地址 -->
    <AddressQuickSelectionPopup
      v-model:visible="showAddressPopup"
      :show-tips="true"
      :tips-text="logisticsServicesInfo.predictContent"
      @select="handleAddressSelect"
      @close="handleAddressPopupClose"
    />

    <!-- 悬浮气泡按钮：提供快捷操作入口 -->
    <FloatingBubble :offset="200" :isShowCart="false" />
  </div>
</template>

<script setup>
// ================================================================================================
// 依赖导入区域
// ================================================================================================

// Vue 核心功能导入：提供组件开发所需的基础 API
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
// Vue Router 导入：提供路由功能，用于页面跳转和参数获取
import { useRoute, useRouter } from 'vue-router'
// Pinia 状态管理导入：用户状态和购物车状态管理
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
// 商品相关 API 接口导入：提供商品数据获取和操作功能
import {
  productIntroduction,
  queryPredictSkuPromise,
  checkSkuSale,
  setFrontCache,
  isWhiteUserLimitCheck,
  getLimitAreaList
} from '@api/interface/goods.js'
// 通用组件导入：商品详情页面所需的各种 UI 组件
import GoodsSwiper from '@components/Common/GoodsSwiper.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import LazyGoodsIntroduce from '@components/GoodsDetailCommon/LazyGoodsIntroduce.vue'
import AddressQuickSelectionPopup from '@components/Common/Address/AddressQuickSelectionPopup.vue'
import SpecSelectionPopup from '@components/GoodsDetailCommon/SpecSelectionPopup.vue'
import GoodsDetailSkeleton from '@components/GoodsDetailCommon/GoodsDetailSkeleton.vue'
import GoodsBasicInfo from '@components/GoodsDetailCommon/GoodsBasicInfo.vue'
import MarketingSection from '@components/GoodsDetailCommon/MarketingSection.vue'
import SpecSelection from '@components/GoodsDetailCommon/SpecSelection.vue'
import PromotionActivity from '@components/GoodsDetailCommon/PromotionActivity.vue'
import DeliveryInfo from '@components/GoodsDetailCommon/DeliveryInfo.vue'
import StatusTips from '@components/GoodsDetailCommon/StatusTips.vue'
import ActionBar from '@components/GoodsDetailCommon/ActionBar.vue'
// 工具函数导入：提供各种辅助功能
import { getBizCode } from '@utils/curEnv.js'
import { debounce } from 'lodash-es'
import { closeToast, showLoadingToast, showToast } from 'vant'
// 自定义 Hooks 导入：提供复用的业务逻辑
import { useAlert, useGoodsDetail } from '@/composables/index.js'
// 其他 API 接口导入：订单和地址相关接口
import { getBuyNowGoods, jdAddressCheck } from '@api/index.js'
// 本地存储工具导入：用于缓存购买数据
import { buyProductNow, buyProductNowSession } from '@utils/storage.js'
// 营销活动相关 API 导入
import { getActiveList } from '@api/interface/goods.js'
// 通用工具库导入：URL 处理和分享功能
import { urlAppend } from 'commonkit'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import ShareDropdown from '@components/GoodsDetailCommon/ShareDropdown.vue'
import { getDefaultShareUrl, shareData } from '@utils/share.js'
import { log, setWeiXinShareData, share } from 'commonkit'

// ================================================================================================
// 基础配置和工具初始化区域
// ================================================================================================

// 弹窗提示工具：用于显示各种提示信息
const $alert = useAlert()

// 路由相关：获取当前路由信息和路由跳转功能
const route = useRoute()
const router = useRouter()
// 从路由参数中获取商品 ID 和 SKU ID
const goodsId = route.params.goodsId
const skuId = route.params.skuId

// 状态管理：用户状态和购物车状态
const userStore = useUserStore()
const newCartStore = useNewCartStore()

// 页面控制状态：控制各种功能的显示和行为
const isShowLogisticsServices = ref(true) // 是否显示物流服务信息
const initialLoadComplete = ref(false) // 初始加载是否完成
const hasUpdatedUrlForSku = ref(false) // 是否已更新 URL 中的 SKU ID
const lazyGoodsIntroduceRef = ref(null) // 懒加载商品介绍组件引用

// 商品详情业务逻辑 Hook：提供商品数据管理和规格选择功能
const {
  spu, // 商品 SPU 数据
  curSpecs, // 当前选中的规格
  curSkuId, // 当前选中的 SKU ID
  querySpu, // 查询 SPU 数据方法
  querySku, // 查询 SKU 数据方法
  querySpecsList, // 查询规格列表方法
  setSpecs, // 设置规格方法
  queryCurSpecs, // 查询当前规格方法
  queryDisabledSpecs, // 查询禁用规格方法
  isSpecsComplete // 检查规格是否完整方法
} = useGoodsDetail(goodsId, skuId)

// ================================================================================================
// 响应式状态定义区域
// ================================================================================================

// 从store获取购物车数量
const cartCount = computed(() => {
  return newCartStore.countByGoods
})

// 页面加载和显示控制相关状态
const isLoading = ref(true) // 页面整体加载状态
const contentLoaded = ref(false) // 内容加载完成状态
const detailErr = ref(false) // 商品详情加载错误状态

// 弹窗显示控制相关状态
const showSpecPopup = ref(false) // 规格选择弹窗显示状态
const showAddressPopup = ref(false) // 地址选择弹窗显示状态
const showShareMenu = ref(true) // 分享菜单显示状态

// 用户交互相关状态
const specActionType = ref(0) // 规格选择操作类型：1-加入购物车，2-立即购买
const goodsNum = ref(1) // 用户选择的商品数量
const previewImageIndex = ref(0) // 图片预览索引

// 商品相关数据状态
const isJD = ref(false) // 是否为京东商品
const productIntroductionData = ref('') // 商品详情介绍数据
const logisticsServicesInfo = ref({ // 物流服务信息
  logisticsType: 1, // 物流类型
  returnRuleStr: '', // 退货规则字符串
  returnRule: [], // 退货规则数组
  deliveryRuleStr: '', // 配送规则字符串
  deliveryRule: [], // 配送规则数组
  predictContent: '预计48小时之内发货',
  isJD: false
})

// 营销活动相关状态：处理各种营销活动和促销信息
const marketingTemplates = ref([]) // 营销模板列表
const electronicData = ref({}) // 电子发票相关数据
const activeList = ref([]) // 活动列表数据

// 地区和用户限制相关状态：处理商品的地区限制和用户权限
const limitAreaList = ref([]) // 限制地区列表
const isWhiteUser = ref(false) // 是否为白名单用户
const isLimitArea = ref(false) // 是否为限制地区

// 各种数据加载状态：控制不同数据模块的加载状态显示
const isLimitAreaLoading = ref(false) // 限制地区数据加载状态
const isWhiteUserLoading = ref(false) // 白名单用户检查加载状态
const isElectronicLoading = ref(false) // 电子发票数据加载状态
const isMarketingLoading = ref(false) // 营销数据加载状态
const isActiveListLoading = ref(false) // 活动列表加载状态
const isProductIntroductionLoading = ref(false) // 商品介绍加载状态
const isLogisticsServicesLoading = ref(false) // 物流服务信息加载状态
const isSkuSaleLoading = ref(false) // SKU销售状态加载状态
const isPredictSkuLoading = ref(false) // SKU预测数据加载状态
const isFrontCacheLoading = ref(false) // 前端缓存数据加载状态

// 商品销售和预测相关数据：处理商品的销售状态和预测信息
const skuSaleData = ref({}) // SKU销售数据
const predictSkuData = ref({}) // SKU预测数据
const frontCacheData = ref({}) // 前端缓存数据

// 地址管理相关状态：处理用户收货地址的选择和管理
const addressList = ref([]) // 用户地址列表
const addressPopupVisible = ref(false) // 地址选择弹窗显示状态
const selectedAddress = ref(null) // 当前选中的地址
const addressLoading = ref(false) // 地址数据加载状态
const addressError = ref('') // 地址相关错误信息

// 规格选择弹窗相关状态：处理商品规格选择的弹窗交互
const specPopupVisible = ref(false) // 规格选择弹窗显示状态
const specPopupType = ref('') // 弹窗类型：'cart'(加入购物车) 或 'buy'(立即购买)

// 商品数量管理：控制用户选择的商品数量
const quantity = ref(1) // 当前选择的商品数量，默认为1

// 图片预览功能相关状态：处理商品图片的放大预览
const imagePreviewVisible = ref(false) // 图片预览弹窗显示状态
const previewImages = ref([]) // 预览图片列表
const previewStartPosition = ref(0) // 预览开始位置索引

// 视频播放功能相关状态：处理商品视频的播放
const videoVisible = ref(false) // 视频播放弹窗显示状态
const videoUrl = ref('') // 当前播放的视频URL

// 轮播图控制相关状态：管理商品图片轮播
const swiperRef = ref(null) // 轮播组件引用
const currentSlide = ref(0) // 当前轮播图索引

// 分享功能相关状态：处理商品分享到各平台
const shareDropdownVisible = ref(false) // 分享下拉菜单显示状态
const shareUrl = ref('') // 分享链接URL
const shareTitle = ref('') // 分享标题
const shareDesc = ref('') // 分享描述
const shareImgUrl = ref('') // 分享图片URL

// ================================================================================================
// 商品状态控制区域
// ================================================================================================

// 数据获取状态：控制商品数据的获取和显示状态
const isDataGet = ref(false) // 商品数据是否已获取

// 商品销售状态：控制商品的销售和库存状态
const isOnSale = ref(false) // 商品是否在售
const isInStock = ref(false) // 商品是否有库存

// 用户选择状态：控制用户的选择完整性
const isSpecsSelected = ref(false) // 规格是否已选择
const isAddressSelected = ref(false) // 地址是否已选择
const isUserLoggedIn = ref(false) // 用户是否已登录

// 按钮状态控制：控制各种操作按钮的可用性
const isCartButtonDisabled = ref(false) // 加入购物车按钮是否禁用
const isBuyButtonDisabled = ref(false) // 立即购买按钮是否禁用

// 操作加载状态：控制各种操作的加载状态显示
const isAddToCartLoading = ref(false) // 加入购物车操作加载状态
const isBuyNowLoading = ref(false) // 立即购买操作加载状态
const isGoToCartLoading = ref(false) // 跳转购物车操作加载状态

// 商品限制状态：控制商品的各种限制条件
const limitState = ref(true) // 白名单用户是否限购，true没有限购，false有限购
const regionalSalesState = ref(true) // 所选区域暂不支持销售，true没有区域限制，false有区域限制

// 营销活动相关数据
const marketTemplatesType1 = ref([]) // 营销位类型1
const marketTemplatesType4 = ref([]) // 营销位类型4
const reducePrice = ref(0) // 减免价格

// 规格选择区域的引用
const specOptionsRef = ref(null)
const specSelectionRef = ref(null)

const specsData = computed(() => {
  return {
    specsList: querySpecsList(),
    curSpecs: queryCurSpecs ? queryCurSpecs() : [],
    disabledSpecs: queryDisabledSpecs ? queryDisabledSpecs() : [],
    currentSku: currentSku.value
  }
})

// 限购逻辑
const xgObj = computed(() => {
  const limitTemplate = spu.value?.limitTemplate
  let limitText = ''
  if (limitTemplate && limitTemplate?.limitCountType) {
    switch (limitTemplate.limitCountType) {
      case '1':
        limitText = `每人每次限购${limitTemplate.limitNum}件`
        break
      case '2':
        limitText = `每人限购${limitTemplate.limitNum}件`
        break
      default:
        limitText = ''
    }
  }
  const limitNum = limitTemplate && limitTemplate?.limitNum ? limitTemplate.limitNum : 1
  return {
    isXg: spu.value?.isXg === '1',
    limitNum,
    limitText
  }
})

// 起购逻辑
const lowestBuyObj = computed(() => {
  const lowestBuyValue = currentSku.value?.lowestBuy
  const isLowestBuy = lowestBuyValue ? parseInt(lowestBuyValue, 10) > 1 : false
  const lowestBuyNum = lowestBuyValue ? parseInt(lowestBuyValue, 10) : 1
  const lowestBuyText = lowestBuyValue ? `${lowestBuyNum}件起购` : ''

  return {
    isLowestBuy,
    lowestBuyNum,
    lowestBuyText
  }
})


const addressInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo
  console.warn(2131331, curAddrInfo);

  return {
    provinceName: curAddrInfo.provinceName || '',
    cityName: curAddrInfo.cityName || '',
    countyName: curAddrInfo.countyName || '',
    townName: curAddrInfo.townName || '',
    addrDetail: curAddrInfo.addrDetail || '',
    receiverName: curAddrInfo.recName || '',
    receiverPhone: curAddrInfo.recPhone || ''
  }
})

const skuGoodsInfo = computed(() => {
  const sku = currentSku.value
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 确定购买限制
  let purchaseLimit = 999 // 默认限制
  let purchaseLimitType = 'none'
  let purchaseLimitText = ''

  // 优先处理起购逻辑
  if (lowestBuy.isLowestBuy) {
    purchaseLimit = lowestBuy.lowestBuyNum
    purchaseLimitType = 'minimum'
    purchaseLimitText = lowestBuy.lowestBuyText
  }

  // 处理限购逻辑
  if (xg.isXg && xg.limitText) {
    purchaseLimit = xg.limitNum
    purchaseLimitType = xg.limitText.includes('每次') ? 'perTime' : 'perPerson'
    purchaseLimitText = xg.limitText
  }

  return {
    image: sku.listImageUrl || '',
    price: sku.price || 0,
    supplierSkuId: sku.supplierSkuId || '',
    stock: sku.stock || 0,
    purchaseLimit,
    purchaseLimitType,
    purchaseLimitText,
    currSku: sku,
    xgObj: xg,
    lowestBuyObj: lowestBuy
  }
})

// 防抖的异步数据更新函数
const debouncedUpdateGoodsInfo = debounce(async () => {
  const sku = querySku()
  if (sku && sku.supplierCode && sku.supplierCode.indexOf('jd_') > -1) {
    try {
      await Promise.all([
        queryPredictSku(),
        getProductIntroduction()
      ])
    } catch (error) {
      console.error('更新商品信息失败:', error)
    }
  }
}, 300)

// 懒加载相关逻辑已移至 LazyGoodsIntroduce 组件



// 滚动位置管理
let savedScrollPosition = 0
const saveScrollPosition = () => {
  savedScrollPosition = window.scrollY || document.documentElement.scrollTop
}

const restoreScrollPosition = () => {
  requestAnimationFrame(() => {
    window.scrollTo(0, savedScrollPosition)
  })
}

// 计算属性
const currentSku = computed(() => {
  return querySku() || {}
})

const goodsInfo = computed(() => {
  const sku = currentSku.value
  if (!sku || !spu.value) {
    return {
      name: '',
      price: 0,
      originalPrice: 0,
      imageUrl: ''
    }
  }
  return {
    name: sku.name || '',
    price: sku.price || 0,
    originalPrice: sku.originalPrice || 0,
    imageUrl: sku.listImageUrl || ''
  }
})

const currentSKU = computed(() => {
  const sku = currentSku.value
  return {
    ...sku
  }
})

const goodsMediaList = computed(() => {
  const sku = currentSku.value
  if (!sku || !sku.detailImageUrl) {
    return []
  }
  return sku.detailImageUrl.map(url => ({
    type: 'image',
    url,
    alt: '商品图片'
  }))
})

const specOptions = computed(() => {
  // 获取规格数据，参考 GoodsChoose.vue 的逻辑
  const specsList = querySpecsList()
  const curSpecs = queryCurSpecs ? queryCurSpecs() : []
  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  // 如果没有规格数据，返回默认规格结构
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return {
      specsList: [],
      curSpecs: ['默认规格'], // 默认选中默认规格
      curDisabledSpecs: [],
    }
  }

  return {
    specsList: specsList,
    curSpecs: curSpecs,
    curDisabledSpecs: disabledSpecs,
  }
})

// 缓存的规格选项数据，在商品初始化时计算一次
const cachedSpecOptions = ref([])

// 初始化规格选项数据
const initSpecOptions = () => {
  if (!spu.value || !spu.value.skuList) {
    cachedSpecOptions.value = []
    return
  }

  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  const options = spu.value.skuList.map(sku => {
    const isDisabled = checkSkuDisabled(sku, disabledSpecs)

    return {
      id: sku.skuId,
      name: getSkuDisplayName(sku),
      image: sku.listImageUrl,
      disabled: isDisabled,
      skuData: sku,
    }
  })

  // 只缓存可选择的SKU（过滤掉禁用的）
  cachedSpecOptions.value = options.filter(option => !option.disabled)

  console.log('初始化规格选项数据:', {
    totalOptions: options.length,
    selectableOptions: cachedSpecOptions.value.length
  })
}

// 动态计算选中状态的规格选项
const enhancedSpecOptions = computed(() => {
  return cachedSpecOptions.value.map(option => ({
    ...option,
    selected: option.id === curSkuId.value
  }))
})


// 检查SKU是否被禁用
const checkSkuDisabled = (sku, disabledSpecs) => {
  const skuSpecs = getSkuSpecs(sku)
  return skuSpecs.some(spec => disabledSpecs.includes(spec))
}

// 获取SKU的规格数组
const getSkuSpecs = (sku) => {
  const { param, param1, param2, param3 } = sku
  return ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
    .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
      p !== '_p2_undefined' && p !== '_p3_undefined')
}

const selectedSpec = computed(() => {
  const sku = currentSku.value
  const specsList = querySpecsList()

  // 如果没有规格数据，显示默认规格
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return `默认规格 ${goodsNum.value}件`
  }

  if (!sku || !isSpecsComplete()) {
    return `默认 ${goodsNum.value}件`
  }

  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)

  const specText = specs.join('，') || '默认'
  return `${specText} ${goodsNum.value}件`
})

const deliveryInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo
  const location = `${curAddrInfo.provinceName} ${curAddrInfo.cityName} ${curAddrInfo.countyName} ${curAddrInfo.townName || ''}`.trim()

  return {
    location: location || '配送地址',
    predictContent: logisticsServicesInfo.value.predictContent || (isJD.value ? 'JD配送' : '普通配送'),
    returnPolicy: logisticsServicesInfo.value.returnRuleStr || '7天无理由退货',
    service: '店铺售后由沃百富商城提供服务'
  }
})

/**
 * 商品详情页面数据加载主方法
 *
 * @description 负责页面加载时的数据初始化和配置，包括商品基础信息、规格数据、营销活动等
 *
 * @features
 * - 商品基础信息获取：SPU数据、SKU数据、规格信息
 * - 营销活动处理：营销模板、电子发票、活动列表
 * - 用户权限检查：白名单用户、地区限制、登录状态
 * - 物流服务：配送信息、预测时间、退货政策
 * - 页面状态管理：加载状态、错误状态、内容显示
 * - URL管理：SKU ID更新、路由参数保持
 *
 * @workflow
 * 1. 设置物流服务显示状态
 * 2. 获取商品SPU基础数据
 * 3. 验证数据有效性和商品状态
 * 4. 检查并设置京东商品标识
 * 5. 更新URL中的SKU ID（如需要）
 * 6. 初始化规格选项和营销数据
 * 7. 设置页面显示状态
 * 8. 异步加载额外信息（京东商品）
 * 9. 执行用户权限和地区限制检查
 * 10. 初始化分享功能
 *
 * @error_handling
 * - 商品信息更新中：显示提示信息
 * - 数据获取失败：显示错误消息
 * - 商品数据为空：记录警告并停止加载
 * - URL更新失败：记录警告但不影响主流程
 *
 * @performance
 * - 基础数据优先加载，确保页面快速显示
 * - 额外信息异步加载，不阻塞主流程
 * - 使用防抖优化规格切换性能
 * - 懒加载商品介绍内容
 */
const loadGoodsDetail = async () => {
  // 根据业务代码设置物流服务显示状态
  isShowLogisticsServices.value = getBizCode() !== 'fupin'

  try {
    // 获取商品SPU基础数据
    const json = await querySpu()

    // 验证商品数据获取结果
    if (json.code !== '0000') {
      if (json.code === '8888') {
        console.warn('此商品信息更新中，暂时无法购买，请您选购其他商品。')
      } else {
        console.warn(json.msg)
      }
      detailErr.value = true
      isLoading.value = false
      return
    }

    // 检查商品数据是否为空
    if (!spu.value) {
      console.warn('商品数据为空')
      isLoading.value = false
      return
    }

    // 检查当前SKU并判断是否为京东商品
    const sku = querySku()
    if (sku && sku.supplierCode) {
      isJD.value = sku.supplierCode.indexOf('jd_') > -1
    }

    // URL管理：如果进入页面时没有传 skuId，但已选择了默认规格，则把 skuId 更新到 URL 上（保留原有 query）
    if (!route.params.skuId && !hasUpdatedUrlForSku.value && sku && sku.skuId) {
      try {
        await router.replace({
          name: 'zq-goods-detail',
          params: { goodsId, skuId: sku.skuId },
          query: route.query
        })
        hasUpdatedUrlForSku.value = true
      } catch (e) {
        console.warn('更新 URL skuId 失败:', e)
      }
    }

    // 初始化商品规格选项数据
    initSpecOptions()

    // 处理营销活动相关数据
    processMarketingTemplates()

    // 基础数据加载完成，设置页面显示状态
    isLoading.value = false
    isDataGet.value = true

    // 等待DOM更新后再执行后续操作
    await nextTick()

    // 添加内容加载动画效果
    setTimeout(() => {
      contentLoaded.value = true
      // 标记初始加载完成，用于后续交互优化
      initialLoadComplete.value = true
    }, 100)

    // 京东商品特殊处理：异步加载额外信息，不阻塞页面渲染
    if (isJD.value) {
      // 移除 getProductIntroduction()，改为懒加载模式
      Promise.all([
        queryPredictSku(), // 查询物流预测信息
        checkIsSkuSale() // 检查SKU销售状态
      ]).catch(error => {
        console.error('加载额外商品信息失败:', error)
      })
    }

    // 数据加载完成后，延迟滚动到选中的规格位置
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 200)

    // 根据起购要求设置初始购买数量
    const lowestBuy = lowestBuyObj.value
    if (lowestBuy.isLowestBuy) {
      goodsNum.value = lowestBuy.lowestBuyNum
    }

    // 初始化懒加载商品介绍组件
    setTimeout(() => {
      lazyGoodsIntroduceRef.value?.init()
    }, 500)

    // 用户权限和限制检查
    // 检查白名单用户购买限制
    await checkWhiteUserLimit()

    // 查询商品地区销售限制
    await querySale()

    // 如果用户已登录，进行地址验证
    if (userStore.isLogin) {
      await addressCheck()
    }

    // 初始化分享功能
    await shareInit()

  } catch (error) {
    console.error('加载商品详情失败:', error)
    detailErr.value = true
    isLoading.value = false
  }
}

/**
 * 获取商品详情介绍数据
 * 功能说明：获取商品的详细介绍内容，包括图文描述、规格参数等
 * 使用场景：商品详情页面底部的详细介绍区域
 * 性能优化：支持懒加载，避免首屏加载时间过长
 */
const getProductIntroduction = async () => {
  // 获取当前选中的SKU信息
  const sku = querySku()
  if (!sku) {
    console.warn('无法获取SKU信息，跳过商品介绍加载')
    return
  }

  // 提取供应商相关参数
  const { supplierSkuId, supplierCode } = sku

  // 调用商品介绍接口
  const [err, json] = await productIntroduction({
    supplierSkuId, // 供应商SKU ID
    supplierCode   // 供应商代码
  })

  if (!err) {
    // 直接更新响应式数据，触发页面重新渲染
    productIntroductionData.value = json
  } else {
    console.error('获取商品介绍失败:', err)
  }
}

/**
 * 查询物流配送时间预测
 * 功能说明：根据用户地址和商品信息，预测配送时间
 * 数据来源：供应商物流接口（如京东物流API）
 * 使用场景：商品详情页显示预计配送时间
 * 依赖条件：用户地址信息、SKU供应商信息
 */
const queryPredictSku = async () => {
  // 获取用户当前地址信息
  const info = userStore.curAddressInfo

  // 构建地址信息JSON字符串，用于物流预测接口
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,     // 省份ID
    provinceName: info.provinceName, // 省份名称
    cityId: info.cityId,             // 城市ID
    cityName: info.cityName,         // 城市名称
    countyId: info.countyId,         // 区县ID
    countyName: info.countyName,     // 区县名称
    townId: info.townId,             // 街道ID
    townName: info.townName          // 街道名称
  })

  // 获取当前选中的SKU信息
  const sku = currentSku.value

  // 检查必要的供应商信息是否完整
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) {
    // 缺少供应商信息时，使用默认配送时间
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      predictContent: '预计48小时之内发货'
    }
    return
  }

  // 构建物流预测查询参数
  const params = {
    supplierCode: sku.supplierCode,     // 供应商代码
    supplierSkuId: sku.supplierSkuId,   // 供应商SKU ID
    skuNum: goodsNum.value,             // 购买数量
    addressInfoStr: addressInfo         // 地址信息字符串
  }

  // 调用物流预测接口
  const [err, res] = await queryPredictSkuPromise(params)

  if (!err) {
    // 成功获取预测信息，更新物流服务数据
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res // 包含预测配送时间、配送方式等信息
    }
    return
  }

  // 接口调用失败时，使用默认配送时间
  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    predictContent: '预计48小时之内发货'
  }
}

/**
 * 检查SKU是否可售
 * 功能说明：验证当前SKU的销售状态和物流服务信息
 * 数据来源：供应商销售状态接口
 * 返回信息：物流类型、退货规则、服务政策等
 * 使用场景：确保商品可正常购买和配送
 */
const checkIsSkuSale = async () => {
  // 获取当前选中的SKU信息
  const sku = currentSku.value

  // 检查必要的供应商信息是否完整
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) {
    console.warn('缺少SKU供应商信息，跳过销售状态检查')
    return
  }

  // 构建销售状态查询参数
  const params = {
    supplierCode: sku.supplierCode,   // 供应商代码
    supplierSkuId: sku.supplierSkuId  // 供应商SKU ID
  }

  // 调用销售状态检查接口
  const [err, res] = await checkSkuSale(params)

  if (!err && res && res.length > 0) {
    // 成功获取销售状态信息，更新物流服务数据
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res[0] // 包含物流类型、退货规则等信息
    }
    return
  }

  // 接口调用失败或无数据时，设置默认值
  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    logisticsType: 0,    // 默认物流类型
    returnRuleStr: ''    // 清空退货规则
  }
}

// 获取SKU的显示名称
const getSkuDisplayName = (sku) => {
  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)
  return specs.join('，') || '默认'
}

// 根据SKU更新规格状态
const updateSpecsFromSku = (sku) => {
  const specs = []
  if (sku.param) specs.push('_p0_' + sku.param)
  if (sku.param1) specs.push('_p1_' + sku.param1)
  if (sku.param2) specs.push('_p2_' + sku.param2)
  if (sku.param3) specs.push('_p3_' + sku.param3)

  // 更新当前规格状态
  curSpecs.value = specs
}

// ===================== 用户权限和限制检查方法区域 =================================
/**
 * 检查白名单用户购买限制
 * 功能说明：验证当前用户是否有权限购买特定商品
 * 适用场景：
 * - 企业内购商品（仅限企业员工购买）
 * - VIP专享商品（仅限会员购买）
 * - 特殊渠道商品（仅限指定用户群体）
 * 业务逻辑：只有商品设置了白名单检查且用户已登录时才进行验证
 */
const checkWhiteUserLimit = async () => {
  // 获取商品详情数据
  const goodsDetail = spu.value

  // 检查商品是否启用了白名单用户限制
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1') {
    // 只有用户已登录时才进行白名单验证
    if (userStore.isLogin) {
      // 调用白名单用户检查接口
      const [err, json] = await isWhiteUserLimitCheck(goodsId)

      if (!err) {
        // 更新用户购买限制状态
        limitState.value = json
      } else {
        console.error('白名单用户检查失败:', err)
        // 检查失败时，默认限制购买以确保安全
        limitState.value = false
      }
    } else {
      // 用户未登录时，默认限制购买
      limitState.value = false
    }
  }
}

/**
 * 查询商品地区销售限制
 * 功能说明：检查商品在用户当前地址是否可以销售
 * 限制类型：
 * - 地区限售：某些商品仅在特定地区销售
 * - 政策限制：根据法规政策限制销售区域
 * - 物流限制：物流无法覆盖的区域
 * 业务逻辑：只有用户已登录且商品数据正常时才进行检查
 */
const querySale = async () => {
  // 获取用户当前地址信息
  const info = userStore.curAddressInfo

  // 构建地区限制查询参数
  const params = {
    // 地址信息JSON字符串
    area: JSON.stringify({
      provinceId: info.provinceId, // 省份ID
      cityId: info.cityId,         // 城市ID
      countyId: info.countyId,     // 区县ID
      townId: info.townId          // 街道ID
    }),
    goodsIdList: goodsId // 商品ID列表
  }

  // 只有商品数据正常时才进行区域限购查询
  if (!detailErr.value) {
    // 只有用户已登录时才进行地区限制检查
    if (userStore.isLogin) {
      // 调用地区限制查询接口
      const [err, json] = await getLimitAreaList(params)

      if (!err && json) {
        // 如果返回的限制列表为空，说明该地区可以销售
        regionalSalesState.value = json.length <= 0
      } else {
        console.error('查询地区销售限制失败:', err)
        // 查询失败时，默认允许销售
        regionalSalesState.value = true
      }
    }
    // 标记数据获取完成
    isDataGet.value = true
  } else {
    // 商品数据异常时，标记数据获取失败
    isDataGet.value = false
  }
}

/**
 * 地址有效性检查方法
 * 功能说明：验证用户当前收货地址是否符合配送要求
 * 检查内容：
 * - 地址格式是否正确
 * - 地址是否精确到街道级别
 * - 地址是否在配送范围内
 * 业务场景：京东等第三方商品需要精确的地址信息进行配送
 * 返回值：true-地址有效，false-地址无效
 */
const addressCheck = async () => {
  // 显示地址检查加载提示
  showLoadingToast()

  try {
    // 调用京东地址检查接口
    const [err, json] = await jdAddressCheck()

    // 关闭加载提示
    closeToast()

    // 检查接口调用是否出错
    if (err) {
      showToast(err.msg)
      return false
    }

    // 检查地址是否有效
    if (!json) {
      // 地址无效时，提示用户修改地址
      $alert({
        title: '',
        message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
        confirmButtonText: '修改地址',
        cancelButtonText: '取消',
        showCancelButton: true,
        onConfirmCallback: () => {
          // 用户确认修改地址，跳转到地址编辑页面
          router.push({
            name: 'address-edit',
            query: {
              addrId: userStore.curAddressInfo.addressId, // 当前地址ID
              isInvalid: '1' // 标记地址无效，需要重新设置
            }
          })
        }
      })
      return false
    }

    // 地址检查通过
    return true

  } catch (error) {
    // 关闭加载提示
    closeToast()
    console.error('地址检查异常:', error)
    showToast('地址检查失败，请稍后重试')
    return false
  }
}

// ====================== 营销活动处理方法区域 ==============================
/**
 * 处理营销活动模板数据
 * 功能说明：解析和分类商品的营销活动信息
 * 模板类型：
 * - Type 1: 电子券类营销活动（优惠券、代金券等）
 * - Type 4: 促销活动类营销活动（满减、折扣等）
 * 业务逻辑：根据用户登录状态决定是否获取电子券信息
 */
const processMarketingTemplates = () => {
  // 获取商品详情数据
  const goodsDetail = spu.value

  // 检查商品数据和营销模板是否存在
  if (!goodsDetail || !goodsDetail.marketTemplates) {
    console.warn('商品无营销活动数据')
    return
  }

  // 按营销位类型分类活动信息
  // Type 1: 电子券类营销活动（优惠券、代金券等）
  marketTemplatesType1.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '1'
  )

  // Type 4: 促销活动类营销活动（满减、折扣等）
  marketTemplatesType4.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '4'
  )

  // 如果用户已登录且存在电子券类活动，获取详细的电子券信息
  if (userStore.isLogin && marketTemplatesType1.value.length > 0) {
    getElectronic()
  }
}

/**
 * 获取电子券活动详情
 * 功能说明：获取特定电子券活动的详细信息和优惠金额
 * 适用场景：
 * - 用户已登录且存在电子券类营销活动
 * - 特定模板号（wxy618）的保证金活动
 * 数据更新：更新 reducePrice 状态，用于显示优惠金额
 */
const getElectronic = async () => {
  // 检查用户登录状态和电子券活动数据
  if (userStore.isLogin && marketTemplatesType1.value && marketTemplatesType1.value.length > 0) {
    // 检查是否为特定的保证金活动（wxy618）
    if (
      marketTemplatesType1.value?.[0] &&
      marketTemplatesType1.value[0].reqType === '1' &&
      marketTemplatesType1.value[0].templateNo === 'wxy618'
    ) {
      // 调用活动列表接口获取优惠信息
      const [err, json] = await getActiveList({ templateNo: marketTemplatesType1.value[0].templateNo || '' })
      if (!err) {
        // 更新优惠价格状态
        reducePrice.value = json
      }
    }
  }
}

/**
 * 营销按钮点击处理
 * 功能说明：处理电子券类营销活动的按钮点击事件
 * 业务逻辑：
 * - 检查营销活动数据有效性
 * - 根据请求类型和模板号执行不同的跳转逻辑
 * - 特殊处理保证金活动（wxy618）的参数拼接和重复参与检查
 * 跳转方式：页面重定向到营销活动页面
 */
const marketingBtn = () => {
  // 检查电子券类营销活动数据是否存在
  if (!marketTemplatesType1.value || marketTemplatesType1.value.length === 0) {
    return
  }

  // 获取营销活动配置信息
  const { reqType, reqUrl, templateNo } = marketTemplatesType1.value[0]

  // reqType=1 表示跳转链接形式的营销活动
  if (reqType === '1') {
    if (templateNo === 'wxy618') {
      // 保证金活动特殊处理：需要拼接商品ID、SKU ID和回调地址
      const host = window.location.origin
      const path = import.meta.env.VITE_BASE_URL
      const callbackUrl = host + path + `/goodsdetail/${goodsId}/${currentSku.value.skuId}?distri_biz_code=ziying`

      // 检查用户是否已经参与过该活动（有优惠且商品价格为0）
      if (reducePrice.value > 0 && Number(goodsInfo.value.price) === 0) {
        showToast('您已经参与过该活动，请下次再试吧')
        return
      }

      // 跳转到保证金活动页面，携带商品信息和回调地址
      window.location.href = urlAppend(reqUrl, {
        goodsId: goodsId,
        skuId: currentSku.value.skuId,
        callback: callbackUrl
      })
    } else {
      // 其他类型的营销活动直接跳转
      window.location.href = reqUrl
    }
  }
}

/**
 * 跳转到促销活动详情页面
 * 功能说明：处理促销活动类营销活动的跳转逻辑
 * 参数处理：
 * - 解析原始URL并添加商品相关参数
 * - 添加goodsId、skuId和callback回调地址
 * 业务校验：
 * - 检查商品库存状态
 * - 无货时提示用户选择其他商品
 * 错误处理：捕获跳转异常并给出友好提示
 */
const goToPromotionDetail = (item) => {
  // 获取促销活动的请求URL
  const { reqUrl } = item

  // 解析原始URL对象
  const urlObj = new URL(reqUrl)
  const currentUrl = window.location.href

  // 添加商品相关参数到URL
  urlObj.searchParams.set('goodsId', goodsId)
  urlObj.searchParams.set('skuId', currentSku.value.skuId)
  urlObj.searchParams.set('callback', encodeURIComponent(currentUrl))

  // 获取拼接完成的完整URL
  const url = urlObj.toString()

  // 检查当前商品库存状态
  if (!stockState.value) {
    showToast('抱歉，所选商品暂时无货，请选择其他商品办理。')
  } else {
    try {
      // 跳转到促销活动页面
      window.location.href = url
    } catch (error) {
      // 捕获跳转异常并记录错误
      console.error('跳转保证金页面失败:', error)
      showToast('跳转失败，请稍后再试')
    }
  }
}

// ================================================================================================
// 规格选择处理方法区域
// ================================================================================================

/**
 * 选择商品规格
 * 功能说明：处理用户选择商品规格的逻辑
 * 支持场景：
 * - 完整规格对象选择（包含skuData）
 * - 默认规格选择（字符串形式）
 * - 规格字符串选择（调用setSpecs方法）
 * 数据更新：
 * - 更新当前SKU ID和规格组合
 * - 重置购买数量（考虑起购要求）
 * - 触发相关数据更新和页面滚动
 */
const selectSpec = (spec) => {
  console.log('选择规格:', spec)

  // 处理完整规格对象选择（包含skuData的情况）
  if (spec && spec.skuData) {
    // 更新当前选中的SKU ID
    curSkuId.value = spec.skuData.skuId

    // 从SKU数据中提取规格参数并构建规格组合
    const { param, param1, param2, param3 } = spec.skuData
    curSpecs.value = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
      .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
        p !== '_p2_undefined' && p !== '_p3_undefined')

    // 切换规格时重置购买数量，优先考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1

  } else if (typeof spec === 'string') {
    // 处理字符串形式的规格选择
    if (spec === '默认规格') {
      // 默认规格处理：检查是否存在规格数据
      // 如果没有规格数据，将当前规格设置为空数组
      if (!querySpecsList() || querySpecsList().length === 0 || querySpecsList()[0].length === 0) {
        curSpecs.value = []
      }
    } else {
      // 其他规格字符串：调用原有的setSpecs方法处理
      setSpecs(spec)
    }

    // 切换规格时重置购买数量，优先考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1
  }

  // 在下一个DOM更新周期触发商品信息更新
  nextTick(() => {
    debouncedUpdateGoodsInfo()
  })

  // 延迟执行滚动动画，确保DOM完全更新后再滚动到选中规格
  setTimeout(() => {
    scrollToSelectedSpec()
  }, 100)
}

/**
 * 自动滚动到选中的规格
 * 功能说明：将选中的规格选项滚动到可视区域中心位置
 * 实现原理：
 * - 等待DOM完全更新后查找选中的规格元素
 * - 计算滚动位置使选中元素居中显示
 * - 使用平滑滚动动画提升用户体验
 * 容错处理：
 * - 检查容器和选中元素是否存在
 * - 确保滚动位置不超出边界范围
 * 性能优化：使用requestAnimationFrame确保在正确时机执行滚动
 */
const scrollToSelectedSpec = async () => {
  console.log('开始执行 scrollToSelectedSpec')

  // 等待多个DOM更新周期，确保所有相关元素完全渲染
  await nextTick()
  await nextTick()

  // 通过子组件引用获取规格选项容器元素
  const container = specSelectionRef.value?.specOptionsRef
  if (!container) {
    console.log('specOptionsRef 不存在')
    return
  }

  // 查找当前选中的规格元素（使用正确的CSS选择器）
  const selectedSpecElement = container.querySelector('.spec-option.is-active')
  console.log('找到的选中元素:', selectedSpecElement)

  if (!selectedSpecElement) {
    // 容错处理：如果没有找到选中元素，记录调试信息
    console.log('未找到选中的规格元素，尝试查找所有规格元素')
    const allSpecs = container.querySelectorAll('.spec-option')
    console.log('所有规格元素:', allSpecs)
    return
  }

  // 获取容器和选中元素的尺寸信息
  const containerWidth = container.clientWidth
  const selectedElementLeft = selectedSpecElement.offsetLeft
  const selectedElementWidth = selectedSpecElement.offsetWidth

  console.log('滚动计算参数:', {
    containerWidth,
    selectedElementLeft,
    selectedElementWidth,
    scrollWidth: container.scrollWidth
  })

  // 计算目标滚动位置：让选中的规格元素在容器中居中显示
  const targetScrollLeft = selectedElementLeft - (containerWidth / 2) + (selectedElementWidth / 2)

  // 边界检查：确保滚动位置不会超出容器的可滚动范围
  const maxScrollLeft = container.scrollWidth - containerWidth
  const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

  console.log('滚动目标位置:', {
    targetScrollLeft,
    maxScrollLeft,
    finalScrollLeft
  })

  // 使用 requestAnimationFrame 确保在正确的时机执行滚动
  requestAnimationFrame(() => {
    console.log('执行滚动到位置:', finalScrollLeft)
    container.scrollTo({
      left: finalScrollLeft,
      behavior: 'smooth'
    })
  })
}


// =====================  购物车和购买操作方法区域  =================================
/**
 * 处理商品数量变化
 * 功能说明：验证和更新用户选择的商品购买数量
 * 验证规则：
 * - 起购要求：检查是否满足最低购买数量
 * - 限购要求：检查是否超出最大购买数量
 * 数据更新：更新 goodsNum 状态
 * 用户提示：数量不符合要求时给出相应提示
 */
const handleQuantityChange = (quantity) => {
  // 获取限购和起购配置信息
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 验证起购要求：检查是否满足最低购买数量
  if (lowestBuy.isLowestBuy && quantity < lowestBuy.lowestBuyNum) {
    console.warn(`最少购买${lowestBuy.lowestBuyNum}件哦！`)
    // 自动调整为最低购买数量
    goodsNum.value = lowestBuy.lowestBuyNum
    return
  }

  // 验证限购要求：检查是否超出最大购买数量
  if (xg.isXg && quantity > xg.limitNum) {
    console.warn(`超出限购数量：${xg.limitText}`)
    // 自动调整为最大限购数量
    goodsNum.value = xg.limitNum
    return
  }

  // 数量验证通过，更新商品购买数量
  goodsNum.value = quantity
}

/**
 * 处理添加到购物车操作
 * 功能说明：将当前选中的商品规格和数量添加到购物车
 * 前置检查：
 * - 用户登录状态验证
 * - 商品规格完整性检查
 * - 商品状态和库存验证
 * 业务流程：
 * - 构建地址信息参数
 * - 调用购物车添加接口
 * - 处理成功/失败结果
 * 用户体验：显示加载状态和操作结果提示
 */
const handleAddToCart = async () => {
  // 检查用户登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录时引导用户进行登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败，终止操作
      return
    }
  }

  // 关闭规格选择弹窗
  showSpecPopup.value = false

  // 检查商品规格是否选择完整
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 检查商品状态：0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查商品库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }

  // 构建用户地址信息参数
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  // 显示加载提示
  showLoadingToast()

  try {
    // 调用购物车添加接口
    const err = await newCartStore.add({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo
    })

    // 关闭加载提示
    closeToast()

    if (err) {
      // 添加购物车失败，显示错误信息
      showToast(err.msg)
    } else {
      // 添加购物车成功，显示成功提示并重置数量
      setTimeout(() => {
        showToast('加入购物车成功')
        // 重置商品数量为起购数量或1
        if (lowestBuyObj.value.isLowestBuy) {
          goodsNum.value = +lowestBuyObj.value.lowestBuyNum
        } else {
          goodsNum.value = 1
        }
      }, 0)
    }
  } catch (error) {
    // 捕获异常并显示通用错误提示
    showToast('添加购物车失败，请重试')
  }
}

/**
 * 处理立即购买操作
 * 功能说明：直接跳转到订单确认页面进行购买
 * 前置检查：
 * - 用户登录状态验证
 * - 商品规格完整性检查
 * - 商品状态和库存验证
 * 业务流程：
 * - 构建地址信息参数
 * - 调用立即购买接口
 * - 跳转到订单确认页面
 * 用户体验：显示加载状态和操作结果提示
 */
const handleBuyNow = async () => {
  // 检查用户登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录时引导用户进行登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败，终止操作
      return
    }
  }

  // 关闭规格选择弹窗
  showSpecPopup.value = false

  // 检查商品规格是否选择完整
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 检查商品状态：0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查商品库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }

  // 构建用户地址信息参数
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  // 显示加载提示
  showLoadingToast()

  try {
    // 调用立即购买商品接口
    const [res] = await getBuyNowGoods({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo,
      bizCode: getBizCode('ORDER')
    })

    // 关闭加载提示
    closeToast()

    // 检查接口返回结果
    if (res?.code !== '0000') {
      // 处理地址精确度不足的特殊错误码
      if (res?.code === '1003') {
        $alert({
          title: '',
          message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
          confirmButtonText: '修改地址',
          cancelButtonText: '取消',
          showCancelButton: true,
          confirmButtonColor: '#007EE6',
          cancelButtonColor: '#007EE6',
          onConfirmCallback: () => {
            // 跳转到地址编辑页面
            router.push({
              name: 'address-edit',
              query: {
                addrId: info.addressId,
                isInvalid: '1'
              }
            })
          }
        })
      } else {
        // 显示其他错误信息
        showToast(res?.msg || '购买失败，请重试')
      }
    } else {
      // 立即购买成功，处理后续逻辑

      // 缓存立即购买的数据到本地存储
      buyProductNow.set(res)
      buyProductNowSession.set(res)

      // 调用后端接口缓存数据，确保数据持久化
      showLoadingToast()
      try {
        await setFrontCache({
          content: JSON.stringify(res)
        })
      } catch (cacheError) {
        console.error('缓存数据失败:', cacheError)
      } finally {
        closeToast()
      }

      // 构建订单确认页面的查询参数
      const query = {
        goodsId: currentSku.value.goodsId,
        skuId: currentSku.value.skuId,
        goodsNum: goodsNum.value,
        supplierCode: currentSku.value.supplierCode
      }

      // 传递当前页面的特殊参数到订单页面
      if (route.query.curSelectedMoney) {
        query.curSelectedMoney = route.query.curSelectedMoney
      }
      if (route.query.curSelectedTime) {
        query.curSelectedTime = route.query.curSelectedTime
      }
      if (route.query.orderNo) {
        query.orderNo = route.query.orderNo
      }

      // 跳转到订单确认页面
      router.push({
        path: '/orderconfirm',
        query
      })

      // 重置商品数量为起购数量或1
      if (lowestBuyObj.value.isLowestBuy) {
        goodsNum.value = +lowestBuyObj.value.lowestBuyNum
      } else {
        goodsNum.value = 1
      }
    }
  } catch (error) {
    // 捕获异常并显示通用错误提示
    showToast('购买失败，请重试')
  }
}

/**
 * 添加到购物车按钮点击处理
 * 功能说明：触发规格选择弹窗，用于添加商品到购物车
 * 前置检查：验证购物车按钮是否可用
 * 业务逻辑：设置操作类型为添加购物车(1)，显示规格选择弹窗
 */
const addToCart = () => {
  // 检查购物车按钮是否被禁用
  if (cartButtonDisabledStatus.value) {
    return
  }
  // 设置规格选择操作类型为添加购物车
  specActionType.value = 1
  // 显示规格选择弹窗
  showSpecPopup.value = true
}

/**
 * 跳转到购物车页面
 * 功能说明：直接导航到购物车页面查看已添加的商品
 */
const goToCart = () => {
  // 路由跳转到购物车页面
  router.push({ name: 'cart' })
}

/**
 * 立即购买按钮点击处理
 * 功能说明：触发规格选择弹窗，用于立即购买商品
 * 前置检查：验证购物车按钮是否可用
 * 业务逻辑：设置操作类型为立即购买(2)，显示规格选择弹窗
 */
const buyNow = () => {
  // 检查购物车按钮是否被禁用
  if (cartButtonDisabledStatus.value) {
    return
  }
  // 设置规格选择操作类型为立即购买
  specActionType.value = 2
  // 显示规格选择弹窗
  showSpecPopup.value = true
}

// ==================== 媒体轮播和预览方法区域 ====================

/**
 * 处理图片预览操作
 * 功能说明：点击轮播图片时触发预览功能
 * 支持类型：仅处理图片类型的媒体项
 * 业务逻辑：设置预览图片索引，可扩展为全屏预览
 * @param {Object} param - 预览参数
 * @param {Object} param.item - 媒体项对象
 * @param {number} param.index - 媒体项索引
 */
const handleImagePreview = ({ item, index }) => {
  // 检查媒体类型是否为图片
  if (item.type === 'image') {
    // 设置当前预览图片的索引
    previewImageIndex.value = index
    // 可以在这里实现图片预览功能（如全屏预览、图片放大等）
  }
}

/**
 * 处理视频播放操作
 * 功能说明：点击轮播视频时触发播放功能
 * 业务逻辑：可扩展为视频播放器或全屏播放
 * @param {Object} param - 播放参数
 * @param {Object} param.item - 视频项对象
 */
const handleVideoPlay = ({ item }) => {
  // 输出视频播放日志
  console.log('播放视频:', item)
  // 可以在这里实现视频播放功能（如打开视频播放器、全屏播放等）
}

/**
 * 处理轮播切换事件
 * 功能说明：轮播图切换时的回调处理
 * 业务逻辑：记录当前轮播索引，可用于统计或其他业务需求
 * @param {number} index - 当前轮播索引
 */
const handleSlideChange = (index) => {
  // 输出当前轮播索引日志
  console.log('当前轮播索引:', index)
  // 可以在这里添加轮播切换的业务逻辑（如埋点统计、预加载等）
}

// ==================== 地址选择和管理方法区域 ====================

/**
 * 处理地址点击事件
 * 功能说明：用户点击配送区域时打开地址选择弹窗
 * 业务逻辑：显示地址选择弹窗供用户选择配送地址
 */
const handleAddressClick = () => {
  // 显示地址选择弹窗
  showAddressPopup.value = true
}

/**
 * 处理地址选择完成事件
 * 功能说明：用户选择新地址后的回调处理
 * 业务逻辑：地址变更后重新查询商品相关信息
 * @param {Object} address - 用户选择的新地址信息
 */
const handleAddressSelect = async (address) => {
  // 输出选择的新地址信息
  console.log('选择了新地址:', address)

  // 地址变更后重新查询商品相关信息
  await reloadGoodsInfoAfterAddressChange()
}

/**
 * 处理地址弹窗关闭事件
 * 功能说明：地址选择弹窗关闭时的回调处理
 * 业务逻辑：隐藏地址选择弹窗
 */
const handleAddressPopupClose = () => {
  // 隐藏地址选择弹窗
  showAddressPopup.value = false
}

/**
 * 地址变更后重新查询商品信息
 * 功能说明：地址变更后重新获取与地址相关的商品信息
 * 查询内容：
 * - 京东商品的物流配送时间
 * - SKU可售状态检查
 * - 白名单用户限制检查
 * - 商品销售区域限制检查
 * - 地址有效性检查
 * 异常处理：捕获并记录查询过程中的错误
 */
const reloadGoodsInfoAfterAddressChange = async () => {
  try {
    // 如果是京东商品，需要重新查询物流信息
    if (isJD.value) {
      // 重新查询物流配送时间
      await queryPredictSku()

      // 重新检查SKU是否可售
      await checkIsSkuSale()
    }

    // 重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 重新查询商品限制销售区域
    await querySale()

    console.log('地址变更后商品信息更新完成')
  } catch (error) {
    console.error('地址变更后更新商品信息失败:', error)
  }

  // 如果用户已登录，进行地址有效性检查
  if (userStore.isLogin) {
    await addressCheck()
  }
}

// ==================== 计算属性区域 ====================

/**
 * 商品上架状态计算属性
 * 功能说明：判断商品是否处于可购买的上架状态
 * 状态说明：
 * - state = 0: 不能购买
 * - state = 1: 上架
 * - state = 2: 下架
 * - state = null: 状态异常
 * @returns {boolean} true-可购买，false-不可购买
 */
const onSaleState = computed(() => {
  // 检查当前SKU状态，如果状态为'2'（下架）则不可购买
  return !(currentSku.value && currentSku.value.state === '2')
})

/**
 * 商品库存状态计算属性
 * 功能说明：判断商品是否有库存
 * @returns {boolean} true-有库存，false-无库存
 */
const stockState = computed(() => {
  // 检查当前SKU是否存在且库存大于0
  return currentSku.value && currentSku.value.stock > 0
})

/**
 * 用户购买资格状态计算属性
 * 功能说明：检查用户是否具备购买该商品的资格
 * 检查逻辑：
 * - 如果商品需要检查白名单用户且用户已登录，返回limitState的值
 * - 其他情况默认有购买资格
 * @returns {boolean} true-有购买资格，false-无购买资格
 */
const userStatus = computed(() => {
  // 获取商品详情信息
  const goodsDetail = spu.value

  // 如果商品需要检查白名单用户且用户已登录，则返回limitState的值
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1' && userStore.isLogin) {
    return limitState.value
  }

  // 其他情况默认有购买资格
  return true
})

/**
 * 购物车按钮禁用状态计算属性
 * 功能说明：综合判断购物车按钮是否应该禁用
 * 禁用条件：
 * - 数据未加载完成
 * - 商品未上架
 * - 商品无库存
 * - 用户无购买资格
 * - 不在销售区域
 * - 超出购买限制
 * @returns {boolean} true-禁用，false-启用
 */
const cartButtonDisabledStatus = computed(() => {
  // 如果数据已加载完成，检查各项购买条件
  return isDataGet.value ?
    !onSaleState.value || !stockState.value || !userStatus.value || !regionalSalesState.value || !limitState.value :
    true // 数据未加载完成时禁用
})

// ==================== 监听器区域 ====================

/**
 * 监听地址变化，重新查询物流信息
 * 功能说明：当用户切换配送地址时，重新查询相关信息
 * 监听内容：用户当前地址信息的变化
 * 触发条件：省市区县任一级别地址发生变化
 * 处理逻辑：
 * - 京东商品重新查询物流配送时间
 * - 已登录用户重新查询购物车数据
 */
const watchAddress = computed(() => userStore.curAddressInfo)
watch(watchAddress, async (newAddr, oldAddr) => {
  // 检查地址是否真正发生变化（省市区县任一级别）
  if (newAddr && oldAddr && (
    newAddr.provinceId !== oldAddr.provinceId ||
    newAddr.cityId !== oldAddr.cityId ||
    newAddr.countyId !== oldAddr.countyId ||
    newAddr.townId !== oldAddr.townId
  )) {
    // 如果是京东商品，重新查询物流配送时间
    if (isJD.value) {
      await queryPredictSku()
    }

    // 地址切换时重新查询购物车数据
    if (userStore.isLogin) {
      try {
        await newCartStore.query()
        console.log('地址切换后购物车数据已更新')
      } catch (error) {
        console.error('地址切换后更新购物车数据失败:', error)
      }
    }
  }
}, { deep: true })

/**
 * 监听规格数据变化
 * 功能说明：监听商品规格选择和禁用状态的变化
 * 监听内容：当前选中规格和禁用规格的变化
 * 业务用途：用于调试和日志记录规格数据更新情况
 */
watch([curSpecs, () => queryDisabledSpecs()], ([newCurSpecs, newDisabledSpecs]) => {
  // 输出规格数据更新日志
  console.log('规格数据更新:', {
    curSpecs: newCurSpecs,
    disabledSpecs: newDisabledSpecs
  })
}, { deep: true })

/**
 * 监听当前SKU ID变化，触发滚动
 * 功能说明：当SKU发生变化时，自动滚动到对应的规格选项
 * 触发条件：SKU ID发生变化且初始加载已完成
 * 延迟处理：延迟150ms执行滚动，确保DOM更新完成
 */
watch(curSkuId, (newSkuId, oldSkuId) => {
  // 检查SKU ID是否真正发生变化且初始加载已完成
  if (newSkuId && newSkuId !== oldSkuId && initialLoadComplete.value) {
    console.log('SKU ID 变化，触发滚动:', { newSkuId, oldSkuId })

    // 延迟执行滚动，确保DOM更新完成
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 150)
  }
})

/**
 * 监听登录状态变化，重新检查白名单用户限制
 * 功能说明：当用户登录状态发生变化时，重新获取相关信息
 * 处理逻辑：
 * - 重新检查白名单用户限制
 * - 重新获取营销活动信息
 * - 重新查询购物车数据
 */
watch(() => userStore.isLogin, async (newLoginStatus, oldLoginStatus) => {
  // 检查登录状态是否真正发生变化且商品数据已加载
  if (newLoginStatus !== oldLoginStatus && spu.value) {
    // 重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 如果用户登录了且存在营销活动，重新获取营销活动信息
    if (newLoginStatus && marketTemplatesType1.value.length > 0) {
      await getElectronic()
    }

    // 登录状态变化时重新查询购物车数据
    if (newLoginStatus) {
      try {
        await newCartStore.query()
        console.log('登录后购物车数据已更新')
      } catch (error) {
        console.error('登录后更新购物车数据失败:', error)
      }
    }
  }
})

// ==================== 生命周期钩子区域 ====================

/**
 * 组件挂载时的初始化操作
 * 功能说明：页面加载时执行的初始化逻辑
 * 初始化内容：
 * - 加载商品详情数据
 * - 注册滚动事件监听器
 * - 已登录用户查询购物车数据
 */
onMounted(async () => {
  // 加载商品详情数据
  loadGoodsDetail()

  // 注册滚动事件监听器，实时保存滚动位置
  window.addEventListener('scroll', saveScrollPosition, { passive: true })

  // 页面进入时判断登录状态，如果登录了就查询购物车
  if (userStore.isLogin) {
    try {
      await newCartStore.query()
      console.log('页面加载时购物车数据已更新')
    } catch (error) {
      console.error('页面加载时更新购物车数据失败:', error)
    }
  }
})

/**
 * 组件卸载时的清理操作
 * 功能说明：页面销毁时执行的清理逻辑
 * 清理内容：
 * - 移除滚动事件监听器，防止内存泄漏
 */
onUnmounted(() => {
  // 清理滚动事件监听器，防止内存泄漏
  window.removeEventListener('scroll', saveScrollPosition)
})

// ==================== 分享功能方法区域 ====================

/**
 * 分享功能初始化
 * 功能说明：初始化商品分享数据，设置微信分享信息
 * 分享内容：
 * - 分享标题：商品名称
 * - 分享描述：根据业务代码生成不同的描述文案
 * - 分享图片：商品主图
 * - 分享链接：商品详情页链接
 * 业务代码说明：
 * - ziying: 自营商品
 * - fupin: 扶贫商品
 * - fulihui: 福利汇商品
 * - lnzx: 联农智选商品
 */
const shareInit = async () => {
  // 获取当前业务代码
  const bizCode = getBizCode()

  // 根据业务代码生成分享描述文案
  const intro = () => {
    const sku = spu.value?.skuList?.[0] || currentSku.value
    if (bizCode === 'ziying') {
      return sku.comment || '足不出户囤遍好物！购商品，来精选。'
    } else if (bizCode === 'fupin') {
      return sku.comment || '消费帮扶，共献爱心，乡村振兴，有我联通。'
    } else if (bizCode === 'fulihui') {
      return sku.comment || '足不出户囤遍好物！购商品，来福利汇。'
    } else if (bizCode === 'lnzx') {
      return sku.comment || '联农智选，好货甄选，品质可信。'
    } else {
      return sku.comment || sku.name || sku.merchantName || ''
    }
  }

  // 获取第一个SKU信息
  const firstSku = spu.value?.skuList?.[0] || currentSku.value

  // 设置分享数据
  shareData.title = firstSku.name || goodsInfo.value.name // 分享标题
  shareData.describe = intro() // 分享描述
  shareData.picUrl = goodsInfo.value.imageUrl || '' // 分享图片
  shareData.link = await getDefaultShareUrl() // 分享链接

  // 输出分享信息日志
  log('[GOODS-DETAIL] shareInfo', shareData)

  // 设置微信分享数据
  setWeiXinShareData(shareData)
}

/**
 * 处理右上角分享点击事件
 * 功能说明：用户点击右上角分享按钮时触发分享功能
 * 注意事项：分享功能不能写在异步函数中
 * @param {Event} e - 点击事件对象
 */
const onDropdownShare = (e) => {
  // 调用分享功能（注意：分享功能不能写在异步函数中）
  share(shareData, e)
}

// ==================== 营销活动相关计算属性区域 ====================

/**
 * 是否存在类型1营销活动计算属性
 * 功能说明：判断是否存在电子券类营销活动
 * @returns {boolean} true-存在，false-不存在
 */
const hasMarketingType1 = computed(() => {
  return marketTemplatesType1.value && marketTemplatesType1.value.length > 0
})

/**
 * 是否存在类型4营销活动计算属性
 * 功能说明：判断是否存在其他类型营销活动
 * @returns {boolean} true-存在，false-不存在
 */
const hasMarketingType4 = computed(() => {
  return marketTemplatesType4.value && marketTemplatesType4.value.length > 0
})
</script>

<style scoped lang="less">
.goods-detail {
  min-height: 100vh;
  background-color: #FFFFFF;
  padding-bottom: 55px;
}

.goods-content {
  background-color: #FFFFFF;
}

.image-section {
  background-color: #FFFFFF;
  padding: 0;
}

// 组件样式已移至各自的组件文件中

// 商品介绍样式已移至 LazyGoodsIntroduce 组件

// 样式已移至各自的组件文件中
</style>
