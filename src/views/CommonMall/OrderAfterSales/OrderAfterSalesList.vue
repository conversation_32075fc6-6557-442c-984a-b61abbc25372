<template>
  <div class="after-sales-list" ref="contentRef">
    <AfterSalesListSkeleton v-if="showSkeleton" :count="3" />

    <AfterSalesEmptyState v-else-if="!loading && isEmptyState && finished" />

    <van-pull-refresh v-else v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        loading-text="加载中..."
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <AfterSalesOrderItem
          v-for="order in orderList"
          :key="order.id"
          :order-data="order"
          :action-buttons="generateActionButtons(order, actionOptions)"
        />
      </van-list>
    </van-pull-refresh>

    <ExpirationPopup
      v-model:visible="expirationPopupVisible"
      title=""
      main-text="抱歉，订单已过售后申请时效"
      sub-text="商品已超过售后期限，如需售后可联系客服处理"
      confirm-text="确定"
      @close="expirationPopupVisible = false"
      @confirm="expirationPopupVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, toRefs } from 'vue'
import { isEmpty, debounce } from 'lodash-es'
import { closeToast, showLoadingToast } from 'vant'
import { getAfterSalesInfo } from '@api/interface/order.js'
import { getBizCode } from '@utils/curEnv.js'
import ExpirationPopup from '@components/Common/ExpirationPopup/ExpirationPopup.vue'
import { useOrderAfterSalesActions } from '@/composables/useOrderAfterSalesActions.js'
import AfterSalesListSkeleton from './components/AfterSalesListSkeleton.vue'
import AfterSalesEmptyState from './components/AfterSalesEmptyState.vue'
import AfterSalesOrderItem from './components/AfterSalesOrderItem.vue'

const props = defineProps({
  scrollPosition: {
    type: Number,
    default: 0
  }
})

const { scrollPosition } = toRefs(props)

const {
  expirationPopupVisible,
  generateActionButtons
} = useOrderAfterSalesActions()

const emit = defineEmits(['update-scroll'])
const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalPage = ref(0)
const error = ref(false)
const isRefreshing = ref(false)
const refreshing = ref(false)

const actionOptions = computed(() => ({
  showAddToCart: false,
  useSubOrderData: false
}))

const showSkeleton = computed(() => {
  return loading.value && isEmpty(orderList.value) && !error.value
})

const isEmptyState = computed(() => {
  return isEmpty(orderList.value)
})

const handleScroll = debounce(() => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    emit('update-scroll', scrollTop)
  }
}, 16)

const onLoad = async () => {
  try {
    const params = {
      bizCode: getBizCode('ORDER'),
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    showLoadingToast()
    const [err, json] = await getAfterSalesInfo(params)
    closeToast()

    if (err) {
      error.value = true
      loading.value = false
      finished.value = true
      return
    }

    currentPage.value++
    loading.value = false

    if (!json?.afterSaleList?.length) {
      finished.value = true
      return
    }

    const expandedOrders = json.afterSaleList.flatMap(order =>
      order.map(item => ({
        ...item,
        skuNumInfoList: item.skuNumInfoList,
        price: item.orderPrice,
        totalPrice: item.orderPrice
      }))
    )

    if (isRefreshing.value) {
      orderList.value = expandedOrders
      isRefreshing.value = false
    } else if (currentPage.value === 2) {
      orderList.value = expandedOrders
    } else {
      orderList.value.push(...expandedOrders)
    }

    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    if (currentPage.value === 2 && scrollPosition.value > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPosition.value
        }
      })
    }
  } catch (err) {
    console.error('加载数据失败:', err)
    error.value = true
    loading.value = false
    finished.value = true
    closeToast()
  }
}
const refreshData = async () => {
  try {
    isRefreshing.value = true
    orderList.value = []
    currentPage.value = 1
    finished.value = false
    error.value = false
    loading.value = true
    await onLoad()
  } catch (err) {
    console.error('刷新数据失败:', err)
    isRefreshing.value = false
    throw err
  }
}

const onRefresh = async () => {
  try {
    await refreshData()
  } catch (error) {
    console.error('下拉刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

onMounted(() => {
  const target = contentRef.value || window
  target.addEventListener('scroll', handleScroll, { passive: true })

  loading.value = true
  onLoad()
})

onUnmounted(() => {
  const target = contentRef.value || window
  target.removeEventListener('scroll', handleScroll)
})

defineExpose({
  refreshData
})

</script>

<style scoped lang="less">
.after-sales-list {
  min-height: 100vh;
  padding: 10px;
  background: #F8F9FA;
}

:deep(.van-pull-refresh) {
  height: 100vh;
  overflow: auto;
}
</style>
