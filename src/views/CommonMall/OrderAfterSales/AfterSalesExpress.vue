<template>
  <div class="express-page">
    <header>
      <div class="line">
        <p class="title">订单号：</p>
        <p class="content">{{ orderId }}</p>
      </div>
      <div class="line">
        <p class="title">承运商：</p>
        <p class="content">{{ expressName || '--' }}</p>
      </div>
      <div class="line">
        <p class="title">运单号：</p>
        <p class="content">
          <span>{{ expressNo || '--' }}</span>
          <i
            v-if="expressNo"
            class="copy-btn"
            v-clipboard:copy="expressNo"
            v-clipboard:success="onClipboardCopy"
            v-clipboard:error="onClipboardError">
            复制
          </i>
        </p>
      </div>
    </header>
    <main v-if="orderTrackStatus===1">
      <p class="info" v-for="(item, index) in orderTrack" :key="index">
        <span class="content">{{ item.content || item.context }}</span>
        <span class="time">{{ item.msgTime || item.time }}</span>
      </p>
    </main>
    <main v-if="orderTrackStatus===2" style="padding: 10px 20px">
      <p class="no-info" style="padding-bottom: 10px">
        您可通过复制物流单号，前往物流公司官网查询物流情况，也可快速访问"快递100"进行查询。快递100：
        <a href="https://www.kuaidi100.com">https://www.kuaidi100.com</a>
      </p>
      <p class="prompt">(建议前往官方网站查询，当查询失效时，可检查单号是否填写正确)</p>
    </main>
    <main v-if="orderTrackStatus===0" style="padding-left: 17px">
      <div class="empty"/>
      <p class="prompt" style="text-align: center">暂无消息~</p>
    </main>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useClipboard } from '@vueuse/core'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { getAfterSalesExpress } from '@api/interface/order.js'

// 路由参数
const route = useRoute()
const orderId = ref('')
const applySaleApplyId = ref('')
const expressName = ref('')
const expressNo = ref('')
const orderExpress = ref({})
const curOrder = ref({})
const orderTrack = ref([])
const orderTrackStatus = ref(-1) // 1-有快递记录，2-有快递但无记录，0-无快递信息（其他情况）,-1-数据读取中

// 获取售后快递信息
const getExpress = async (applySaleApplyId) => {
  showLoadingToast()
  try {
    const [err, json] = await getAfterSalesExpress(applySaleApplyId)
    if (!err) return json
    return {}
  } finally {
    closeToast()
  }
}

// 剪切板复制
const { copy } = useClipboard()
const copyExpressNo = async () => {
  try {
    await copy(expressNo.value)
    showToast('复制成功')
  } catch (e) {
    console.error('[ORDER-EXPRESS] clipboard error', e)
    showToast('复制失败')
  }
}

// 生命周期钩子
onMounted(async () => {
  orderId.value = route.query.orderId
  applySaleApplyId.value = route.query.applySaleApplyId
  const deliverInfo = await getExpress(applySaleApplyId.value)
  expressName.value = deliverInfo.expressName
  expressNo.value = deliverInfo.expressNo
  orderTrack.value = deliverInfo.orderTrack || []
  if (deliverInfo) {
    if (orderTrack.value && orderTrack.value.length > 0) {
      // 有快递记录
      orderTrackStatus.value = 1
    } else {
      // 有快递，暂无记录
      orderTrackStatus.value = 2
    }
  } else {
    // 无快递信息
    orderTrackStatus.value = 0
  }
})

onBeforeUnmount(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.express-page {
  width: 100%;
  min-height: 100vh;
  background: #FFFFFF;

  header {
    padding: 10px;
    width: 100%;
    margin-bottom: 10px;
    line-height: 15px;
    border-bottom: 9px solid #F8F9FA;
    font-size: 13px;
    color: #171E24;
    box-sizing: border-box;
    .line {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .title {
      color: #171E24;
    }

    .content {
      color: #171E24;
    }

    .copy-btn {
      display: inline-block;
      margin-left: 10px;
      width: 48px;
      height: 23px;
      line-height: 23px;
      border: 1px solid var(--wo-biz-theme-color);
      border-radius: 2px;
      font-size: 13px;
      font-style: normal;
      text-align: center;
      color: var(--wo-biz-theme-color);
    }
  }

  main {
    padding: 15px 15px 15px 56px;
    width: 100%;
    background: #FFFFFF;
    box-sizing: border-box;
    .info {
      position: relative;
      padding-bottom: 22px;
      font-size: 14px;
      line-height: 20px;
      text-align: left;
      color: #718096;

      &:before {
        content: '';
        position: absolute;
        z-index: 2;
        left: -32px;
        display: block;
        width: 8px;
        height: 8px;
        background-color: #CBD5E0;
        background-size: 100% 100%;
        border: 2px solid #FFFFFF;
        border-radius: 50%;
      }

      &:after {
        content: '';
        position: absolute;
        z-index: 1;
        left: -26px;
        top: 0;
        display: block;
        width: 1px;
        height: 100%;
        background: #E2E8EE;
      }

      span {
        display: block;

        &.time {
          color: #718096;
        }
      }
    }

    .info:first-child {
      color: #171E24;

      &:before {
        left: -34px;
        width: 12px;
        height: 12px;
        background-color: var(--wo-biz-theme-color);
        border: 3px solid #ffd6b5;
      }
    }

    .info:last-child {
      &:after {
        display: none;
      }
    }

    .no-info {
      position: relative;
      padding-bottom: 22px;
      font-size: 14px;
      line-height: 20px;
      text-align: left;
      color: #171E24;

      &:before {
        content: '';
        position: absolute;
        z-index: 2;
        left: -32px;
        display: block;
        width: 20px;
        height: 20px;
        background-size: 100% 100%;
      }

      a,
      a:visited,
      a:active {
        text-decoration: underline;
        color: #171E24;
      }
    }

    .prompt {
      line-height: 1.3;
      font-size: 13px;
      color: #718096;
    }

    .empty {
      margin: 31px auto;
      width: 208px;
      height: 161px;
      background: #FFFFFF url(./assets/empty.png) no-repeat 0 0;
      background-size: 100% 100%;
    }
  }
}
</style>
