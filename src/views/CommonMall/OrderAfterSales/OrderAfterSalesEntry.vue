<template>
  <div class="after-sales-entry">
    <section class="product-info">
      <img class="product-info__image" :src="afterSalesProductInfo.detailImageUrl" :alt="afterSalesProductInfo.name">
      <div class="product-info__details">
        <h3 class="product-info__name">{{ afterSalesProductInfo.name }}</h3>
        <p class="product-info__spec">{{ afterSalesProductInfo.spec }}</p>
      </div>
    </section>

    <div class="section-divider"></div>

    <section class="service-options">
      <div class="service-option" :class="{ 'service-option--disabled': !supportAfterSalesTypes1 }"
        @click="supportAfterSalesTypes1 && goAfterSalesPage(1)">
        <div class="service-option__content">
          <img class="service-option__icon" src="./assets/icon-goods.png" alt="退款图标">
          <div class="service-option__info">
            <h4 class="service-option__title">我要退款</h4>
            <p class="service-option__description">未收到货/已拒收，或与商家协商一致不用退货只退款</p>
            <p v-if="!supportAfterSalesTypes1" class="service-option__notice">
              暂不支持申请退款，请联系在线客服
            </p>
          </div>
        </div>
        <img class="service-option__arrow" src="./assets/arrow.png" alt="进入">
      </div>

      <div class="service-option" :class="{ 'service-option--disabled': !supportAfterSalesTypes2 }"
        @click="supportAfterSalesTypes2 && goAfterSalesPage(2)">
        <div class="service-option__content">
          <img class="service-option__icon" src="./assets/icon-goods.png" alt="退货退款图标">
          <div class="service-option__info">
            <h4 class="service-option__title">我要退货退款</h4>
            <p class="service-option__description">已收到货，需退还收到的货物</p>
            <p v-if="!supportAfterSalesTypes2" class="service-option__notice">
              暂不支持申请退货，请联系在线客服
            </p>
          </div>
        </div>
        <img class="service-option__arrow" src="./assets/arrow.png" alt="进入">
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getBizCode } from '@utils/curEnv.js'
import { getAfsSupportedType } from '@api/interface/afterSales.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { afterSalesProduct } from '@utils/storage.js'
import { compact, pick } from 'lodash-es'

const router = useRouter()
const afterSalesProductInfo = reactive({})
const supportedTypes = ref([])

const supportAfterSalesTypes1 = computed(() => supportedTypes.value.indexOf('1') !== -1)
const supportAfterSalesTypes2 = computed(() => supportedTypes.value.indexOf('2') !== -1)

const goAfterSalesPage = (type) => {
  const data = afterSalesProduct.get()
  const baseQuery = pick(data, ['supplierSubOrderId', 'orderState', 'orderPrice', 'skuNum'])

  const routes = {
    1: { path: '/wo-after-sales-refund', query: baseQuery },
    2: {
      path: '/wo-after-sales-return',
      query: { ...baseQuery, timestamp: Date.now() }
    }
  }

  const route = routes[type]
  if (route) {
    router.replace(route)
  }
}

const getAfsSupportedTypes = async (bizCode, supplierCode, supplierOutSubOrderId) => {
  showLoadingToast()
  try {
    const params = { bizCode, supplierCode, supplierOutSubOrderId }
    const [err, res] = await getAfsSupportedType(params)

    if (!err && Array.isArray(res) && res.length > 0) {
      supportedTypes.value = res
    } else {
      supportedTypes.value = []
      if (err) {
        showToast(err.msg)
      }
    }
  } catch (error) {
    supportedTypes.value = []
    showToast('获取售后类型失败')
  } finally {
    closeToast()
  }
}

onMounted(() => {
  const afterSalesData = afterSalesProduct.get()
  if (!afterSalesData) return

  const { supplierSubOrderId, orderState, orderPrice, skuNum, sku, supplierCode, supplierOutSubOrderId } = afterSalesData

  const paramKeys = ['param', 'param1', 'param2', 'param3', 'param4']
  const params = compact(paramKeys.map(key => sku[key]))

  Object.assign(afterSalesProductInfo, {
    spec: params.join(' '),
    detailImageUrl: sku.detailImageUrl?.[0],
    name: sku.name,
    supplierSubOrderId,
    orderState,
    orderPrice,
    skuNum
  })

  getAfsSupportedTypes(getBizCode(), supplierCode, supplierOutSubOrderId)
})
</script>

<style scoped lang="less">
.after-sales-entry {
  min-height: 100vh;
  background-color: #FFFFFF;
}

.section-divider {
  width: 100%;
  height: 10px;
  background-color: #F8F9FA;
}

.product-info {
  display: flex;
  padding: 10px 17px;
  background-color: #FFFFFF;

  &__image {
    width: 75px;
    height: 75px;
    margin-right: 15px;
    border-radius: 4px;
    object-fit: cover;
  }

  &__details {
    flex: 1;
    overflow: hidden;
  }

  &__name {
    margin: 0 0 10px 0;
    font-size: 15px;
    font-weight: 600;
    color: #171E24;
    line-height: 1.5;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__spec {
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #4A5568;
    line-height: 1.5;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}

.service-options {
  padding: 0 17px;
  background-color: #FFFFFF;
}

.service-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 17px 0;
  border-bottom: 1px solid #E2E8EE;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover:not(&--disabled) {
    background-color: rgba(247, 249, 252, 0.5);
  }

  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &__content {
    display: flex;
    align-items: flex-start;
    flex: 1;
    margin-right: 5px;
  }

  &__icon {
    width: 22px;
    height: 22px;
    margin-right: 5px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
  }

  &__title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #171E24;
    line-height: 1.5;
  }

  &__description {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 400;
    color: #718096;
    line-height: 1.5;
  }

  &__notice {
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #F97316;
    line-height: 1.5;
  }

  &__arrow {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
}
</style>
