<template>
  <WoCard>
    <div class="deposit-info-card" @click="handleViewDeposit">
      <div class="deposit-info-card__info">
        <img src="../../../../../static/images/baozhengjin.png" alt="保证金图标" class="deposit-info-card__icon" />
        <span class="deposit-info-card__label">保证金</span>
      </div>
      <div class="deposit-info-card__amount">
        <span class="deposit-info-card__amount-text">{{ depositAmount }}元存期{{ depositPeriod }}年</span>
      </div>
      <div class="deposit-info-card__action" @click="handleViewDeposit">
        <span class="deposit-info-card__action-text">查看详情</span>
        <img src="../../../../../static/images/arrow-right-gray.png" alt="查看详情" class="deposit-info-card__action-icon" />
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'

const props = defineProps({
  depositAmount: {
    type: [Number, String],
    default: 0
  },
  depositPeriod: {
    type: [Number, String],
    default: 0
  }
})

const emit = defineEmits(['view-deposit'])

const { depositAmount, depositPeriod } = toRefs(props)

const handleViewDeposit = () => {
  emit('view-deposit')
}
</script>

<style scoped lang="less">
.deposit-info-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 4px 0;

  &__info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__icon {
    width: 20px;
    height: 20px;
  }

  &__label {
    font-size: 16px;
    color: #171E24;
    font-weight: 500;
  }

  &__amount {
    flex: 1;
    text-align: center;
  }

  &__amount-text {
    font-size: 14px;
    color: #4A5568;
    font-weight: 400;
  }

  &__action {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  &__action-text {
    font-size: 14px;
    color: #718096;
    font-weight: 400;
  }

  &__action-icon {
    width: 6px;
    height: 10px;
  }
}
</style>
