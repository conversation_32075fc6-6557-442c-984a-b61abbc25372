<template>
  <WoCard>
    <div class="logistics-status-card">
      <div class="logistics-status-card__timeline" v-if="!isCompleted">
        <div class="logistics-status-card__timeline-item">
          <div class="logistics-status-card__dot logistics-status-card__dot--received"></div>
          <div class="logistics-status-card__line" :style="{ minHeight: 25 + 'px' }"></div>
          <div class="logistics-status-card__dot logistics-status-card__dot--pending"></div>
        </div>
      </div>

      <div class="logistics-status-card__content">
        <div class="logistics-status-card__status" @click="handleViewLogistics">
          <div class="logistics-status-card__status-content">
            <div class="logistics-status-card__header">
              <img src="../../assets/package.png" alt="物流图标" class="logistics-status-card__icon" />
              <span class="logistics-status-card__text">
                {{ statusText }}
              </span>
              <span class="logistics-status-card__detail" v-if="lastTrack">
                {{ lastTrack.context || '暂无物流信息' }}
              </span>
              <span class="logistics-status-card__detail" v-else>已上传物流单号，暂无快递信息</span>
            </div>
            <div class="logistics-status-card__arrow" v-if="showArrow">
              <img src="../../assets/arrow-black.png" alt="查看详情" class="arrow-icon" />
            </div>
          </div>
        </div>
        <div class="logistics-status-card__receiver" v-if="!isCompleted">
          <div class="logistics-status-card__receiver-info">
            <div class="logistics-status-card__receiver-name">{{ receiverName }}</div>
            <div class="logistics-status-card__receiver-phone">{{ receiverPhone }}</div>
          </div>
          <div class="logistics-status-card__receiver-address">
            {{ receiverAddress }}
          </div>
        </div>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'

const props = defineProps({
  statusText: {
    type: String,
    default: '暂无物流信息'
  },
  lastTrack: {
    type: Object,
    default: null
  },
  showArrow: {
    type: Boolean,
    default: false
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  receiverName: {
    type: String,
    default: ''
  },
  receiverPhone: {
    type: String,
    default: ''
  },
  receiverAddress: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['view-logistics'])

const {
  statusText,
  lastTrack,
  showArrow,
  isCompleted,
  receiverName,
  receiverPhone,
  receiverAddress
} = toRefs(props)

const handleViewLogistics = () => {
  emit('view-logistics')
}
</script>

<style scoped lang="less">
.logistics-status-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  overflow: hidden;

  &__timeline {
    flex-shrink: 0;
    position: relative;
  }

  &__timeline-item {
    margin-top: 2px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: relative;
    z-index: 2;

    &--received {
      background: var(--wo-biz-theme-color);
    }

    &--pending {
      background: rgba(255, 120, 10, 0.50);
    }
  }

  &__line {
    width: 2px;
    background-color: #E2E8EE;
    transition: height 0.3s ease;
    z-index: 1;
    margin: 5px 0;
    opacity: 0.5;
    background: var(--wo-biz-theme-color);
  }

  &__content {
    flex: 1;
    overflow: hidden;
  }

  &__status {
    cursor: pointer;
  }

  &__status-content {
    display: flex;
    justify-content: space-between;
  }

  &__header {
    font-size: 14px;
    color: #171E24;
    font-weight: 500;
    line-height: 1.4;
    margin: 0;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  &__icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }

  &__text {
    margin-right: 5px;
    flex-shrink: 0;
    color: var(--wo-biz-theme-color);
    font-weight: 600;
  }

  &__detail {
    flex: 1;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__arrow {
    flex-shrink: 0;
    width: 7px;
    height: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  &__receiver {
    margin-top: 16px;
  }

  &__receiver-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #171E24;
    font-weight: 500;
  }

  &__receiver-name {
    margin-right: 12px;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__receiver-address {
    font-size: 12px;
    color: #4A5568;
    font-weight: 400;
    line-height: 1.5;
    display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
  }
}
</style>
