<template>
  <div class="recycle-bin-empty">
    <div class="recycle-bin-empty__content">
      <img :src="emptyImage" alt="暂无订单" class="recycle-bin-empty__image" />
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  emptyImage: {
    type: String,
    default: '../assets/no-data.png'
  }
})

const { emptyImage } = toRefs(props)
</script>

<style scoped lang="less">
.recycle-bin-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  &__content {
    text-align: center;
  }

  &__image {
    width: 120px;
    height: 120px;
    opacity: 0.6;
  }
}
</style>
