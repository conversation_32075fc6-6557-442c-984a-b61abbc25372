<template>
  <div class="recycle-bin">
    <div class="recycle-bin__tip">
      已删除订单无法申请售后，如需操作请先还原订单
    </div>
    <div class="recycle-bin__content" ref="contentRef">
      <RecycleBinSkeleton v-if="showSkeleton"/>

      <RecycleBinEmptyState
        v-if="!showSkeleton && !loading && orderList.length === 0 && finished"
        :empty-image="noDataImage"
      />

      <van-list
        v-else
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <RecycleBinOrderItem
          v-for="order in orderList"
          :key="order.id"
          :order-data="order"
          :is-deleting="order.isDeleting"
          @copy-order="copyOrderNumber"
          @detail-click="onDetailClick"
          @permanent-delete="handlePermanentDelete"
          @restore-order="handleRestoreOrder"
        />
      </van-list>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick, onActivated, onDeactivated, computed } from 'vue'
import { throttle } from 'lodash-es'
import useClipboard from 'vue-clipboard3'
import { showLoadingToast, showToast, closeToast } from 'vant'
import { getOrderRecycleBinList, modOrderListShow } from '@api/index.js'
import { getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/composables/index.js'
import { useRouter } from "vue-router"
import RecycleBinSkeleton from './components/RecycleBinSkeleton.vue'
import RecycleBinEmptyState from './components/RecycleBinEmptyState.vue'
import RecycleBinOrderItem from './components/RecycleBinOrderItem.vue'

const { toClipboard } = useClipboard()
const $alert = useAlert()
const router = useRouter()

const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const showSkeleton = ref(true)
const onLoadGetDataLock = ref(false)
const totalPage = ref(0)
// const error = ref(false)
const finishedText = ref('没有更多了')

const scrollPositions = ref({
  all: 0
})

const noDataImage = computed(() => '../assets/no-data.png')

const updateScrollPosition = (position) => {
  scrollPositions.value.all = position
}

const handleScroll = throttle(() => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    updateScrollPosition(scrollTop)
  }
}, 100)

const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber)
    showToast('复制成功')
  } catch (e) {
    console.error(e)
    showToast('复制失败')
  }
}

const onDetailClick = (order) => {
  router.push({
    path: '/user/order/detail',
    query: {
      orderId: order.id,
      isPay: order.orderState === '0' ? '1' : '2'
    }
  })
}

const handlePermanentDelete = async (order) => {
  try {
     const cancelOrderFn = async () => {
      showLoadingToast()
      const params = {
        supplierOrderId: order.id,
        isDelete: 2
      }
      const [err] = await modOrderListShow(params)
      closeToast()

      if (!err) {
        showToast('订单删除成功!')
        const index = orderList.value.findIndex(item => item.id === order.id)
        if (index !== -1) {
          orderList.value[index].isDeleting = true
          setTimeout(() => {
            orderList.value.splice(index, 1)
            if (orderList.value.length === 0) {
              finishedText.value = ''
            }
          }, 500)
        }
      } else {
        showToast(err.msg || '删除失败')
      }
    }

    $alert({
      title: '',
      message: '永久删除后，您将无法查看及还原订单，确认继续删除吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: async () => {
        await cancelOrderFn()
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      showToast('删除失败')
    }
  }
}

const handleRestoreOrder = async (order) => {
  try {
    showLoadingToast()
    const params = {
      supplierOrderId: order.id,
      isDelete: 0
    }
    const [err] = await modOrderListShow(params)
    closeToast()

    if (!err) {
      showToast('订单已成功还原至"我的订单"')
      window.sessionStorage.setItem('woRecycleBinRestore', 1)

      const index = orderList.value.findIndex(item => item.id === order.id)
      if (index !== -1) {
        orderList.value[index].isDeleting = true
        setTimeout(() => {
          orderList.value.splice(index, 1)
          if (orderList.value.length === 0) {
            finishedText.value = ''
          }
        }, 500)
      }
    } else {
      showToast(err.msg || '还原失败')
    }
  } catch (error) {
    closeToast()
    showToast('还原失败')
  }
}

const onLoad = async () => {
  if (onLoadGetDataLock.value) {
    return
  }

  const params = {
    bizCode: getBizCode('ORDER'),
    pageNo: currentPage.value,
    pageSize: pageSize.value
  }

  onLoadGetDataLock.value = true

  const [err, json] = await getOrderRecycleBinList(params)

  onLoadGetDataLock.value = false
  loading.value = false
  showSkeleton.value = false

  if (!err) {
    currentPage.value++

    if (json?.list.length > 0) {
      finishedText.value = '没有更多了'
    }

    if (json && json?.list.length <= 0) {
      finished.value = true
      return
    }

    orderList.value = [...orderList.value, ...json.list]

    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    if (currentPage.value === 2 && scrollPositions.value.all > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPositions.value.all
        }
      })
    }
  } else {
    showToast(err.msg || '获取回收站数据失败')
  }
}

onMounted(() => {
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }
  onLoad()
})

onUnmounted(() => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
})

onActivated(() => {
  nextTick(() => {
    if (contentRef.value && scrollPositions.value.all > 0) {
      contentRef.value.scrollTop = scrollPositions.value.all
    }
  })
})

onDeactivated(() => {
  if (contentRef.value) {
    scrollPositions.value.all = contentRef.value.scrollTop
  }
})
</script>

<style scoped lang="less">
.recycle-bin {
  height: 100vh;
  display: flex;
  flex-direction: column;

  &__tip {
    background: #FEFAE9;
    color: var(--wo-biz-theme-color);
    padding: 10px 22px;
    font-size: 12px;
    box-sizing: border-box;
  }

  &__content {
    flex: 1;
    overflow: auto;
    background-color: #F8F9FA;
    padding: 10px;
  }
}
</style>
