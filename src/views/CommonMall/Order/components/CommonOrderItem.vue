<template>
  <article class="common-order-item">
    <WoCard>
      <header class="common-order-item__header">
        <div class="common-order-item__number-wrapper">
          <template v-if="isZQ">
            <img src="../../../../assets/images/enterprise_icon.png" alt="企业图标"
              class="common-order-item__enterprise-icon" />
            <span class="common-order-item__supplier-name">{{ order.supplier.name }}</span>
          </template>
          <template v-else>
            <span class="common-order-item__number-text">订单号：{{ order.id }}</span>
            <img src="../../../../static/images/copy.png" alt="复制" class="common-order-item__copy-icon"
              @click.stop="handleCopyOrder" />
          </template>
        </div>

        <OrderCountdown v-if="order.orderState === '0' && order.remainingTime > 0"
          :remaining-time="order.remainingTime" />

        <div v-else class="common-order-item__status" :class="getStatusClass(order.orderState)">
          {{ orderState(order.orderState) }}
        </div>
      </header>

      <section class="common-order-item__goods">
        <OrderGoodsCard :key="order.id" :item="order" :image-size="75" :min-height="110" :showActions="true"
          :moreActions="computedMoreActions" :itemId="order.id" @click="handleDetailClick">
          <template #actions>
            <div ref="buttonsContainerRef" class="buttons-container">
              <WoButton v-for="button in computedVisibleButtons" :key="button.text" :type="getButtonType(button.color)"
                size="small" @click="button.handler">
                {{ button.text }}
              </WoButton>
            </div>
          </template>
        </OrderGoodsCard>
      </section>
    </WoCard>
  </article>
</template>

<script setup>
import { toRefs, ref, computed, onMounted, onUnmounted } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderCountdown from './OrderCountdown.vue'
import orderState from '@utils/orderState.js'
import { useOrderButtons } from '../composables/useOrderButtons.js'
import { getBizCode } from '@utils/curEnv.js'

const props = defineProps({
  order: {
    type: Object,
    required: true
  },
  visibleButtons: {
    type: Array,
    default: () => []
  },
  moreActions: {
    type: Array,
    default: () => []
  }
})

const { order, visibleButtons: originalVisibleButtons, moreActions: originalMoreActions } = toRefs(props)

const emit = defineEmits(['detail-click', 'copy-order'])

// 添加isZQ计算属性
const isZQ = computed(() => getBizCode() === 'zq')

const { getButtonType, getStatusClass } = useOrderButtons()

// 响应式按钮管理
const buttonsContainerRef = ref(null)
const screenWidth = ref(window.innerWidth)

// 监听屏幕宽度变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth)
})

// 按钮排序函数：非gray按钮排在前面
const sortButtonsByPriority = (buttons, prioritizeNonGray = true) => {
  return buttons.sort((a, b) => {
    const aIsGray = a.color === 'gray'
    const bIsGray = b.color === 'gray'

    if (prioritizeNonGray) {
      if (aIsGray && !bIsGray) return 1
      if (!aIsGray && bIsGray) return -1
    } else {
      if (aIsGray && !bIsGray) return -1
      if (!aIsGray && bIsGray) return 1
    }
    return 0
  })
}

// 获取最大可见按钮数量
const getMaxVisibleCount = (screenWidth) => {
  if (screenWidth >= 350 && screenWidth <= 480) return 3
  return 2 // 默认350以下显示2个
}

// 判断按钮是否必须显示
const isMustShowButton = (button, bizCode) => {
  // 非gray按钮必须显示
  if (button.color !== 'gray') return true

  // 扶贫业务时，荣誉证书必须显示
  if (bizCode === 'fupin' && button.text === '荣誉证书') return true

  return false
}

// 计算最终的按钮分配
const computedVisibleButtons = computed(() => {
  // 大屏幕时，直接返回排序后的原始按钮
  if (screenWidth.value > 480) {
    return sortButtonsByPriority([...originalVisibleButtons.value])
  }

  // 小屏幕时的逻辑
  const bizCode = getBizCode()
  const maxVisibleCount = getMaxVisibleCount(screenWidth.value)

  // 合并并排序所有按钮
  const allButtons = [...originalVisibleButtons.value, ...originalMoreActions.value]
  const sortedButtons = sortButtonsByPriority([...allButtons], false)

  // 找出必须显示的按钮
  const mustShowButtons = sortedButtons.filter(button =>
    isMustShowButton(button, bizCode)
  )

  // 如果必须显示的按钮已经足够，直接返回前N个
  if (mustShowButtons.length >= maxVisibleCount) {
    return sortButtonsByPriority(mustShowButtons.slice(0, maxVisibleCount), false)
  }

  // 补充gray按钮
  const remainingSlots = maxVisibleCount - mustShowButtons.length
  const availableGrayButtons = sortedButtons.filter(button =>
    button.color === 'gray' && !isMustShowButton(button, bizCode)
  )

  const finalButtons = [
    ...mustShowButtons,
    ...availableGrayButtons.slice(0, remainingSlots)
  ]

  return sortButtonsByPriority(finalButtons, false)
})

const computedMoreActions = computed(() => {
  // 大屏幕时，使用原始分配
  if (screenWidth.value > 480) {
    return originalMoreActions.value
  }

  // 小屏幕时，计算剩余按钮
  const bizCode = getBizCode()
  const allButtons = [...originalVisibleButtons.value, ...originalMoreActions.value]
  const sortedButtons = sortButtonsByPriority([...allButtons])

  // 获取已显示按钮的文本集合
  const visibleButtonTexts = new Set(
    computedVisibleButtons.value.map(btn => btn.text)
  )

  // 过滤出剩余的gray按钮
  return sortedButtons
    .filter(button => {
      // 排除已显示的按钮
      if (visibleButtonTexts.has(button.text)) return false

      // 只保留gray按钮
      if (button.color !== 'gray') return false

      // 扶贫业务的荣誉证书不放在更多操作中
      if (bizCode === 'fupin' && button.text === '荣誉证书') return false

      return true
    })
    .map(item => ({ ...item, color: 'gray' }))
})

const handleDetailClick = () => {
  emit('detail-click', order.value)
}

const handleCopyOrder = () => {
  emit('copy-order', order.value.id)
}
</script>

<style scoped lang="less">
.common-order-item {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number-wrapper {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__enterprise-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    flex-shrink: 0;
  }

  &__supplier-name {
    font-size: 13px;
    color: #171E24;
    font-weight: 500;
    margin-right: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    ;
  }

  &__number-text {
    font-size: 11px;
    color: #4A5568;
    margin-right: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    ;
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 600;

    &.order-status--unpaid {
      color: var(--wo-biz-theme-color);
    }

    &.order-status--completed {
      color: #4A5568;
    }
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.buttons-container {
  display: flex;
  gap: 8px;
  width: 100%;
  overflow: hidden;
}
</style>
