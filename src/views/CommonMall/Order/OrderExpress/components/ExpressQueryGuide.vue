<template>
  <section class="express-query-guide">
    <div class="express-query-guide__content">
      <p class="express-query-guide__text">
        您可通过复制物流单号，前往物流公司官网查询物流情况，也可快速访问"快递100"进行查询。快递100：
        <a
          href="https://www.kuaidi100.com"
          target="_blank"
          rel="noopener"
          class="express-query-guide__link"
        >
          https://www.kuaidi100.com
        </a>
      </p>
      <p class="express-query-guide__note">
        (建议前往官方网站查询，当查询失效时，可检查单号是否填写正确)
      </p>
    </div>
  </section>
</template>

<script setup>
</script>

<style lang="less" scoped>
.express-query-guide {
  padding: 10px 20px;
  background: #FFFFFF;
  box-sizing: border-box;

  &__content {
    position: relative;
    padding-bottom: 22px;
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    color: #171E24;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 20px;
      height: 20px;
      background-size: 100% 100%;
    }
  }

  &__text {
    margin-bottom: 10px;
    line-height: 1.5;
  }

  &__note {
    line-height: 1.3;
    font-size: 13px;
    color: #718096;
  }

  &__link {
    text-decoration: underline;
    color: #171E24;
  }
}
</style>
