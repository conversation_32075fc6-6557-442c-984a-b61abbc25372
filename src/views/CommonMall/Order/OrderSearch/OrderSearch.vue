<template>
  <div class="order-search">
    <SearchHeader
      v-model="searchKeyword"
      placeholder="搜索我的订单"
      @search="handleSearch"
      class="order-search__header"
    />
    <div class="order-search__content" ref="contentRef">
      <OrderSearchSkeleton v-if="showSkeleton" />
      <OrderSearchEmpty v-if="!showSkeleton && !loading && orderList.length === 0 && finished" />

      <van-list
        v-if="!showSkeleton"
        v-model:loading="loading"
        :finished="finished"
        :finished-text="orderList.length > 0 ? '没有更多了' : ''"
        @load="loadOrderList"
        :immediate-check="false"
      >
        <CommonOrderItem
          v-for="order in orderList"
          :key="order.id"
          :order="order"
          :visible-buttons="getVisibleButtonsForTemplate(order)"
          :more-actions="getMoreActionsForTemplate(order)"
          @detail-click="goToOrderDetail"
          @copy-order="copyOrderNumber"
        />
      </van-list>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick, onActivated, onDeactivated } from 'vue'
import SearchHeader from "@components/Common/SearchHeader.vue"
import CommonOrderItem from '../components/CommonOrderItem.vue'
import OrderSearchSkeleton from './components/OrderSearchSkeleton.vue'
import OrderSearchEmpty from './components/OrderSearchEmpty.vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrderList } from '../composables/useOrderList.js'
import { useOrderActions } from '../composables/useOrderActions.js'
import { useOrderButtons } from '../composables/useOrderButtons.js'
import { useOrderCountdown } from '../composables/useOrderCountdown.js'

const route = useRoute()
const router = useRouter()

const searchKeyword = ref('')
const {
  loading,
  finished,
  orderList,
  showSkeleton,
  contentRef,
  scrollPositions,
  loadOrderList,
  debouncedSearch,
  handleScroll,
  removeOrderItem
} = useOrderList({
  isSearch: true,
  searchKeyword
})

// 使用通用的订单操作逻辑
const orderActions = useOrderActions()

// 使用通用的订单按钮逻辑
const { getVisibleButtons, getMoreActions } = useOrderButtons()

// 使用通用的倒计时逻辑
const { startCountdown, clearAllCountdowns, initOrderCountdowns } = useOrderCountdown()

const createActionConfig = () => ({
  ...orderActions,
  onSuccess: () => {
    removeOrderItem
  }
})

const actionConfig = createActionConfig()


const getVisibleButtonsWithActions = (order) => {
  return getVisibleButtons(order, actionConfig)
}

const getMoreActionsWithActions = (order) => {
  return getMoreActions(order, actionConfig)
}

const performSearch = () => {
  if (!searchKeyword.value) {
    return
  }

  const newQuery = {
    ...route.query,
    keyword: searchKeyword.value
  }

  router.replace({
    path: route.path,
    query: newQuery
  })

  clearAllCountdowns()
  debouncedSearch(searchKeyword.value)
}

const handleSearch = performSearch

// 初始化倒计时
const initCountdowns = (orders) => {
  orders.forEach(order => {
    if (order.orderState === '0' && order.createTime) {
      startCountdown(order)
    }
  })
}

const { copyOrderNumber, goToOrderDetail } = orderActions

const getVisibleButtonsForTemplate = (order) => getVisibleButtonsWithActions(order)
const getMoreActionsForTemplate = (order) => getMoreActionsWithActions(order)


onMounted(() => {
  const keyword = route.query.keyword
  if (keyword) {
    searchKeyword.value = keyword
  }

  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  if (keyword) {
    loadOrderList().then(orders => {
      if (orders) {
        initCountdowns(orders)
      }
    })
  }
})

onUnmounted(() => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
  clearAllCountdowns()
})

onActivated(() => {
  nextTick(() => {
    if (contentRef.value && scrollPositions.value.all > 0) {
      contentRef.value.scrollTop = scrollPositions.value.all
    }
  })
})

onDeactivated(() => {
  if (contentRef.value) {
    scrollPositions.value.all = contentRef.value.scrollTop
  }
})
</script>

<style scoped lang="less">
.order-search {
  height: 100vh;
  display: flex;
  flex-direction: column;

  &__header {
    flex-shrink: 0;
  }

  &__content {
    flex: 1;
    overflow: auto;
    background-color: #F8F9FA;
    padding: 10px;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };
  }
}
</style>
