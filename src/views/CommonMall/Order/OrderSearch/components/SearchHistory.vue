<template>
  <section class="search-history">
    <header class="search-history__header">
      <h2 class="search-history__title">搜索历史</h2>
      <button
        type="button"
        class="search-history__clear-btn"
        @click="handleClearAll"
      >
        <img
          src="../../../../../static/images/delete.png"
          alt="清空"
          class="search-history__clear-icon"
          loading="lazy"
        />
      </button>
    </header>
    <div class="search-history__list">
      <HistoryItem
        v-for="(record, index) in records"
        :key="`history-${index}`"
        :keyword="record"
        @click="handleUseKeyword"
      />
    </div>
  </section>
</template>

<script setup>
import HistoryItem from './HistoryItem.vue'
import { toRefs } from 'vue'

const props = defineProps({
  records: {
    type: Array,
    default: () => []
  }
})

const { records } = toRefs(props)

const emit = defineEmits(['use-keyword', 'clear-all'])

const handleUseKeyword = (keyword) => {
  emit('use-keyword', keyword)
}

const handleClearAll = () => {
  emit('clear-all')
}
</script>

<style scoped lang="less">
.search-history {
  padding: 10px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  &__title {
    font-size: 15px;
    font-weight: 500;
    color: #171E24;
    margin: 0;
  }

  &__clear-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.7;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__clear-icon {
    width: 16px;
    height: 16px;
  }

  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
