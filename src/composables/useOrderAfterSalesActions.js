import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { get } from 'lodash-es'
import dayjs from 'dayjs'
import { useAlert } from '@/composables/index.js'
import { useAfterSalesStore } from '@store/modules/afterSales.js'
import { afterSalesProduct } from '@/utils/storage.js'
import { getBizCode } from '@/utils/curEnv.js'
import {
  applyOrderAfterSalesJD
} from '@/api/interface/order.js'
import {
  applyOrderCancel,
  jdPrompt
} from '@/api/interface/afterSales.js'

/**
 * 订单售后操作 Composable
 * 提供订单售后相关的操作功能，包括退款申请、售后申请、查看详情等
 * 支持京东和非京东供应商的不同处理逻辑
 * 
 * @returns {Object} 订单售后操作管理对象
 * @returns {Ref<boolean>} returns.expirationPopupVisible - 过期提示弹窗显示状态
 * @returns {Function} returns.isExpires - 检查订单是否过期
 * @returns {Function} returns.generateActionButtons - 生成操作按钮
 * @returns {Function} returns.processAfterSalesRequest - 处理售后请求
 * @returns {Function} returns.handleJDAfterSales - 处理京东售后
 * @returns {Function} returns.handleNonJDAfterSales - 处理非京东售后
 * @returns {Function} returns.applyJDRefund - 申请京东退款
 * @returns {Function} returns.applyJDAfterSales - 申请京东售后
 * @returns {Function} returns.applyRefund - 申请退款
 * 
 * @example
 * ```javascript
 * import { useOrderAfterSalesActions } from '@/composables'
 * 
 * // 在组件中使用
 * const {
 *   expirationPopupVisible,
 *   isExpires,
 *   generateActionButtons,
 *   processAfterSalesRequest
 * } = useOrderAfterSalesActions()
 * 
 * // 检查订单是否过期
 * const expired = isExpires(orderItem)
 * 
 * // 生成操作按钮
 * const actions = generateActionButtons(orderItem, {
 *   showRefundButton: true,
 *   showAfterSalesButton: true
 * })
 * 
 * // 处理售后请求
 * processAfterSalesRequest(orderItem, 0) // 0: 退款, 1: 售后, 2: 查看详情
 * ```
 */
export function useOrderAfterSalesActions() {
  const router = useRouter()
  const $alert = useAlert()
  const afterSalesStore = useAfterSalesStore()
  const expirationPopupVisible = ref(false)

  /**
   * 订单状态常量说明：
   * '0': 待付款, '1': 待发货, '2': 已取消, '3': 待发货, '4': 部分发货, '5': 配送中,
   * '6': 部分撤销, '7': 拒收, '8': 已撤销, '9': 已签收, '10': 已退款, '11': 部分退款, 
   * '12': 部分退款中, '-1': 已删除
   */

  /**
   * 检查订单是否过期
   * 订单过期时间为下单后15天
   * 
   * @param {Object} item - 订单项对象
   * @param {Object} [options={}] - 选项配置
   * @param {boolean} [options.useSubOrderData=false] - 是否使用子订单数据
   * @returns {boolean} 订单是否未过期（true表示未过期，false表示已过期）
   */
  const isExpires = (item, options = {}) => {
    const { useSubOrderData = false } = options

    let orderDate
    if (useSubOrderData) {
      orderDate = get(item, 'subOrderRawData.orderDate') || get(item, 'orderDate')
    } else {
      orderDate = get(item, 'subOrderRawData.orderDate') || get(item, 'expireTime') || get(item, 'orderDate')
    }

    if (!orderDate) return false

    const expDate = dayjs(orderDate).add(15, 'day')
    const now = dayjs()
    return expDate > now
  }

  /**
   * 生成操作按钮配置
   * 根据订单状态、供应商类型、售后状态等生成相应的操作按钮
   * 
   * @param {Object} item - 订单项对象
   * @param {Object} [options={}] - 选项配置
   * @param {boolean} [options.showAddToCart=false] - 是否显示加购物车按钮
   * @param {Function} [options.handleAddToCart=null] - 加购物车处理函数
   * @param {boolean} [options.useSubOrderData=false] - 是否使用子订单数据
   * @param {Object} [options.afterSalesInfo=null] - 售后信息
   * @param {boolean} [options.showRefundButton=true] - 是否显示退款按钮
   * @param {boolean} [options.showAfterSalesButton=true] - 是否显示售后按钮
   * @param {boolean} [options.showViewDetailsButton=true] - 是否显示查看详情按钮
   * @returns {Array<Object>} 操作按钮配置数组
   */
  const generateActionButtons = (item, options = {}) => {
    const {
      showAddToCart = false,
      handleAddToCart = null,
      useSubOrderData = false,
      afterSalesInfo = null,
      showRefundButton = true,
      showAfterSalesButton = true,
      showViewDetailsButton = true
    } = options

    const actions = []
    const orderState = item.orderState

    let afterSaleApplyList, isApplyAfterSales, supplierCode

    if (useSubOrderData) {
      const subOrder = item.subOrderRawData
      afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      isApplyAfterSales = afterSaleApplyList.length > 0
      supplierCode = get(subOrder, 'skuNumInfoList[0].sku.supplierCode', '')
    } else {
      isApplyAfterSales = get(item, 'afterSaleId', '')
      supplierCode = get(item, 'skuNumInfoList[0].sku.supplierCode', '')
    }

    const isExpired = !isExpires(item, { useSubOrderData })
    const isJDSupplier = supplierCode.indexOf('jd_') > -1

    // 添加购物车按钮
    if (showAddToCart && handleAddToCart) {
      actions.push({
        key: 'addToCart',
        label: '加购物车',
        type: 'default',
        handler: handleAddToCart
      })
    }

    // zq业务跳过售后逻辑
    const bizCode = getBizCode()
    if (bizCode === 'zq') {
      return actions
    }

    const applyType = useSubOrderData ? afterSalesInfo?.applyType : item.applyType

    if (applyType) {
      if (applyType === '1') {
        // 京东供应商：待发货(1,3)或配送中(5)状态且已申请售后
        if (showRefundButton && isJDSupplier && (orderState === '1' || orderState === '3' || orderState === '5') && isApplyAfterSales) {
          actions.push({
            key: 'applyRefund',
            label: '申请退款',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 0, options) : () => processAfterSalesRequest(item, 0, options)
          })
        // 非京东供应商：待发货(1,3)状态且已申请售后
        } else if (showRefundButton && !isJDSupplier && (orderState === '1' || orderState === '3') && isApplyAfterSales) {
          actions.push({
            key: 'applyRefund',
            label: '申请退款',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 0, options) : () => processAfterSalesRequest(item, 0, options)
          })
        }
      } else if (applyType === '2') {
        // 京东供应商：已签收(9)状态且已申请售后
        if (showAfterSalesButton && isJDSupplier && orderState === '9' && isApplyAfterSales) {
          actions.push({
            key: 'applyAfterSale',
            label: '申请售后',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 1, options) : () => processAfterSalesRequest(item, 1, options)
          })
        // 非京东供应商：配送中(5)或已签收(9)状态且已申请售后
        } else if (showAfterSalesButton && !isJDSupplier && (orderState === '5' || orderState === '9') && isApplyAfterSales) {
          actions.push({
            key: 'applyAfterSale',
            label: '申请售后',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 1, options) : () => processAfterSalesRequest(item, 1, options)
          })
        }
      }
    } else {
      // 京东供应商：待发货(1,3)或配送中(5)状态且未申请售后
      if (showRefundButton && isJDSupplier && (orderState === '1' || orderState === '3' || orderState === '5') && !isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 0, options)
        })
      // 非京东供应商：待发货(1,3)状态且未申请售后
      } else if (showRefundButton && !isJDSupplier && (orderState === '1' || orderState === '3') && !isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 0, options)
        })
      }

      // 京东供应商：已签收(9)状态且未申请售后
      if (showAfterSalesButton && isJDSupplier && orderState === '9' && !isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 1, options)
        })
      // 非京东供应商：配送中(5)或已签收(9)状态且未申请售后
      } else if (showAfterSalesButton && !isJDSupplier && (orderState === '5' || orderState === '9') && !isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 1, options)
        })
      }
    }

    // 查看详情按钮
    if (showViewDetailsButton && isApplyAfterSales) {
      actions.push({
        key: 'viewDetails',
        label: '查看详情',
        type: 'gradient',
        disabled: false,
        handler: () => processAfterSalesRequest(item, 2, options)
      })
    }

    return actions
  }

  /**
   * 处理售后请求
   * 根据请求类型和供应商类型分发到相应的处理函数
   * 
   * @param {Object} item - 订单项对象
   * @param {number} type - 请求类型 (0: 退款, 1: 售后, 2: 查看详情)
   * @param {Object} [options={}] - 选项配置
   * @param {boolean} [options.useSubOrderData=false] - 是否使用子订单数据
   * @param {string} [options.currentOrderStatus=null] - 当前订单状态
   */
  const processAfterSalesRequest = async (item, type, options = {}) => {
    const { useSubOrderData = false, currentOrderStatus = null } = options

    const isExpired = !isExpires(item, { useSubOrderData })

    let hasAfterSaleId, supplierSubOrderId, orderPrice, orderState, supplierSubOutOrderId, skuNum, sku, supplierCode

    if (useSubOrderData) {
      const subOrder = item.subOrderRawData
      const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      hasAfterSaleId = afterSaleApplyList.length > 0

      supplierSubOrderId = get(subOrder, 'id')
      orderPrice = subOrder.orderPrice
      orderState = subOrder.orderState
      supplierSubOutOrderId = subOrder.supplierSubOutOrderId
      const skuInfo = get(subOrder, 'skuNumInfoList[0]', {})
      skuNum = skuInfo.skuNum
      sku = skuInfo.sku
      supplierCode = sku?.supplierCode
    } else {
      hasAfterSaleId = get(item, 'afterSaleId', '')

      supplierSubOrderId = item.supplierSubOrderId
      orderPrice = item.orderPrice
      orderState = item.orderState
      supplierSubOutOrderId = item.supplierSubOutOrderId
      const skuInfo = get(item, 'skuNumInfoList[0]', {})
      skuNum = skuInfo.skuNum
      sku = skuInfo.sku
      supplierCode = sku?.supplierCode
    }

    // 检查是否过期且未申请售后
    if (isExpired && !hasAfterSaleId) {
      expirationPopupVisible.value = true
      return
    }

    // 设置售后商品信息到本地存储
    const afterSalesProductInfo = {
      supplierSubOrderId,
      orderState,
      orderPrice,
      skuNum,
      sku,
      supplierCode,
      supplierOutSubOrderId: supplierSubOutOrderId || ''
    }
    afterSalesProduct.set(afterSalesProductInfo)

    // 重置售后信息
    const applyType = useSubOrderData ? options.afterSalesInfo?.applyType : item.applyType
    if (applyType) {
      afterSalesStore.updateAfterSalesInfo({
        applyType: '',
        afterSaleState: '',
        bizOrderId: '',
        bizCode: '',
        orderState: ''
      })
    }

    const isJDSupplier = supplierCode && supplierCode.indexOf('jd_') > -1

    let giftInfo, afterSaleId, afterSaleApplyType
    if (useSubOrderData) {
      const subOrder = item.subOrderRawData
      giftInfo = get(subOrder, 'giftInfoList', [])
      const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      afterSaleId = afterSaleApplyList[0]?.id
      afterSaleApplyType = afterSaleApplyList[0]?.applyType
    } else {
      giftInfo = +get(item, 'isHaveGift', '0')
      afterSaleId = item.afterSaleId
      afterSaleApplyType = item.applyType
    }

    // 根据供应商类型分发处理
    if (isJDSupplier) {
      await handleJDAfterSales(supplierSubOrderId, supplierCode, type, giftInfo, afterSaleId, afterSaleApplyType, orderState, currentOrderStatus)
    } else {
      await handleNonJDAfterSales(supplierSubOrderId, type, afterSaleId, afterSaleApplyType, orderState)
    }
  }

  /**
   * 处理京东供应商售后
   * 
   * @param {string} supplierSubOrderId - 供应商子订单ID
   * @param {string} supplierCode - 供应商代码
   * @param {number} type - 操作类型 (0: 退款, 1: 售后, 2: 查看详情)
   * @param {Array|number} giftInfo - 赠品信息
   * @param {string} afterSaleId - 售后ID
   * @param {string} applyType - 申请类型
   * @param {string} orderState - 订单状态
   * @param {string} currentOrderStatus - 当前订单状态
   */
  const handleJDAfterSales = async (supplierSubOrderId, supplierCode, type, giftInfo, afterSaleId, applyType, orderState, currentOrderStatus) => {
    try {
      const [err, res] = await jdPrompt({ supplierSubOrderId, supplierCode })
      if (err) {
        showToast(err.msg)
        return
      }

      const prompt = res
      const finalOrderState = currentOrderStatus || orderState

      // 已签收状态(9)的处理
      if (finalOrderState === '9') {
        if (type === 1) {
          const hasGift = Array.isArray(giftInfo) ? giftInfo.length > 0 : giftInfo > 0

          if (hasGift) {
            $alert({
              title: '',
              message: '该商品有赠品，如申请售后，请将赠品一同寄回。',
              confirmButtonText: '确定申请',
              cancelButtonText: '暂不申请',
              showCancelButton: true,
              onConfirmCallback: () => {
                applyJDAfterSales(supplierSubOrderId, 1)
              }
            })
            return
          }
          await applyJDAfterSales(supplierSubOrderId, 1)
        } else if (type === 2) {
          await applyJDAfterSales(supplierSubOrderId, 2)
        }
      } else {
        // 其他状态的处理
        if (type === 0) {
          $alert({
            title: '',
            message: prompt,
            confirmButtonText: '确定申请',
            cancelButtonText: '暂不申请',
            showCancelButton: true,
            onConfirmCallback: () => {
              applyJDRefund(supplierSubOrderId)
            }
          })
        } else if (type === 1) {
          await applyJDAfterSales(supplierSubOrderId, 1)
        } else if (type === 2) {
          if (applyType === '1') {
            router.push({
              path: '/wo-after-sales-detail',
              query: {
                afterSaleId,
                type: +applyType
              }
            })
          } else {
            await applyJDAfterSales(supplierSubOrderId, 2)
          }
        }
      }
    } catch (error) {
      showToast('操作失败，请重试')
      console.error('京东售后处理错误:', error)
    }
  }

  /**
   * 处理非京东供应商售后
   * 
   * @param {string} supplierSubOrderId - 供应商子订单ID
   * @param {number} type - 操作类型 (0: 退款, 1: 售后, 2: 查看详情)
   * @param {string} afterSaleId - 售后ID
   * @param {string} applyType - 申请类型
   * @param {string} orderState - 订单状态
   */
  const handleNonJDAfterSales = async (supplierSubOrderId, type, afterSaleId, applyType, orderState) => {
    try {
      if (type === 0) {
        $alert({
          title: '',
          message: '您确定申请退款吗？',
          confirmButtonText: '确定申请',
          cancelButtonText: '暂不申请',
          showCancelButton: true,
          onConfirmCallback: () => {
            applyRefund(supplierSubOrderId)
          }
        })
      } else if (type === 1) {
        router.push({
          path: '/wo-after-sales-entry',
          query: {
            orderState: orderState
          }
        })
      } else if (type === 2) {
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: +applyType
          }
        })
      }
    } catch (error) {
      showToast('操作失败，请重试')
      console.error('非京东售后处理错误:', error)
    }
  }

  /**
   * 申请京东退款
   * 
   * @param {string} supplierSubOrderId - 供应商子订单ID
   */
  const applyJDRefund = async (supplierSubOrderId) => {
    try {
      showLoadingToast()
      const [err, res] = await applyOrderCancel({ supplierSubOrderId })
      closeToast()

      if (!err) {
        const afterSaleId = res
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: 1
          }
        })
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请退款失败')
      console.error('京东退款申请错误:', error)
    }
  }

  /**
   * 申请京东售后
   * 
   * @param {string} supplierSubOrderId - 供应商子订单ID
   * @param {number} firstUrl - 首次访问URL标识
   */
  const applyJDAfterSales = async (supplierSubOrderId, firstUrl) => {
    try {
      showLoadingToast()
      const [err, json] = await applyOrderAfterSalesJD({
        supplierSubOrderId,
        firstUrl
      })
      closeToast()

      if (!err) {
        window.location.href = json
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请售后失败')
      console.error('京东售后申请错误:', error)
    }
  }

  /**
   * 申请退款（非京东）
   * 
   * @param {string} supplierSubOrderId - 供应商子订单ID
   */
  const applyRefund = async (supplierSubOrderId) => {
    try {
      showLoadingToast()
      const [err, res] = await applyOrderCancel({ supplierSubOrderId })
      closeToast()

      if (!err) {
        const afterSaleId = res
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: 1
          }
        })
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请退款失败')
      console.error('退款申请错误:', error)
    }
  }

  return {
    // 状态
    expirationPopupVisible,
    
    // 方法
    isExpires,
    generateActionButtons,
    processAfterSalesRequest,
    handleJDAfterSales,
    handleNonJDAfterSales,
    applyJDRefund,
    applyJDAfterSales,
    applyRefund
  }
}
